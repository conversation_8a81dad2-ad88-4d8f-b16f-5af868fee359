const CmCommerceReviewsList = {
  id: 'CmCommerceReviewsList',
  status: 'active',
  title: 'Reviews',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    // {
    //   name: 'appmaker/product-counter',
    //   attributes: {
    //     counter: true,
    //   },
    // },
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        // urlListener: onUrlChange,
        // uri: 'https://loox.io/widget/EJbgKbZh35/reviews/{{blockData.product_id}}',

        source: {
          // uri: 'https://loox.io/widget/NkxbSMEnu2/reviews/{{blockData.product_id}}',
          // uri: 'https://loox.io/widget/N1gBr7V_Ih/reviews?h=1655010000000',
          uri: 'https://app-static-pages.appmaker.xyz/shopify/loox/reviewList?shop={{plugins["loox-reviews"].settings.shop}}&product_id={{shopifyIdHelper(blockData.product_id, true)}}',
        },
        // source: '{{blockData.source}}',
        // injectedJavaScript: '{{blockData.injectedJavaScript}}',
      },
    },
  ],
};
export default CmCommerceReviewsList;
