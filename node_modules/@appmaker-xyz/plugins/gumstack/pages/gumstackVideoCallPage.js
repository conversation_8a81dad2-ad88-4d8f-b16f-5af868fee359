import { Platform } from 'react-native';

const onUrlChange = (url, onAction) => {
  // alert(url);
};

const gumstackVideoCallPage = {
  id: 'gumstackVideoCallPage',
  status: 'active',
  title: 'Video Call',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'gumstack-video-call-page',
      attributes: {
        // urlListener: onUrlChange,
        userAgent: `${
          Platform.OS === 'android'
            ? 'gumstack:embed:android'
            : 'gumstack:embed:ios'
        }`,
        source: {
          uri: '{{plugins.gumstack.settings.url}}',
        },
        permissionsRequired: ['camera', 'microphone'],
      },
    },
  ],
};
export default gumstackVideoCallPage;
