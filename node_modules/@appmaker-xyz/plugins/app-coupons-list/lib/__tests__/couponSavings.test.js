import {
  getMinimumPurchaseRequirementAmount,
  getSavingsValue,
} from '../helper';

jest.mock('../../../shopify', () => ({
  currencyHelper: {
    formatMoney: jest.fn(),
  },
}));

describe('Cart Savings', () => {
  it('should return the correct savings', () => {
    const cart = {
      discountType: '',
      discountValue: 10,
      subtotal: 100,
    };
    const result = getSavingsValue(cart);
    expect(result).toEqual(0);
  });
  it('should return the correct savings discount type percentage', () => {
    const cart = {
      discountType: 'percentage',
      discountValue: 10,
      subtotal: 100,
    };
    const result = getSavingsValue(cart);
    expect(result).toEqual(10);
  });
  it('should return the correct savings discount type percentage when', () => {
    const cart = {
      discountType: 'percentage',
      discountValue: '25',
      subtotal: '100',
    };
    const result = getSavingsValue(cart);
    expect(result).toEqual(25);
  });
});
describe('Minimum Purchase Requirement', () => {
  it('should return the correct minimum purchase requirement', () => {
    const cart = {
      requirementType: '',
      subtotal: 100,
      cartQuantity: 0,
      requirementValue: 10,
    };
    const result = getMinimumPurchaseRequirementAmount(cart);
    expect(result).toEqual(0);
  });
  it('should return the correct minimum purchase value', () => {
    const cart = {
      requirementType: 'cart_value',
      subtotal: 100,
      cartQuantity: 0,
      requirementValue: 10,
    };
    const result = getMinimumPurchaseRequirementAmount(cart);
    expect(result).toEqual(-90);
  });
  it('should return the correct minimum string purchase value', () => {
    const cart = {
      requirementType: 'cart_value',
      subtotal: 100,
      cartQuantity: 0,
      requirementValue: '25',
    };
    const result = getMinimumPurchaseRequirementAmount(cart);
    expect(result).toEqual(-75);
  });
  it('should return the correct minimum purchase qty', () => {
    const cart = {
      requirementType: 'cart_quantity',
      subtotal: 100,
      cartQuantity: 5,
      requirementValue: 10,
    };
    const result = getMinimumPurchaseRequirementAmount(cart);
    expect(result).toEqual(5);
  });
});
