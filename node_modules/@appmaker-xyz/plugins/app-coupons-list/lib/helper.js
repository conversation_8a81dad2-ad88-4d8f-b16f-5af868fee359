import { get, isEmpty } from 'lodash';
import { currencyHelper } from '../../../shopify';
import { parse } from '@appmaker-xyz/core';

export function getSavingsValue({
  discountType,
  discountValue,
  subtotal,
  maxDiscountValue,
}) {
  if (discountType === 'percentage') {
    const value = Math.round(
      parseFloat(subtotal) * (parseFloat(discountValue) / 100),
    );
    const maxDiscountValueInt = parseInt(maxDiscountValue, 10);
    if (maxDiscountValueInt && value > maxDiscountValueInt) {
      return maxDiscountValueInt;
    }
    return value;
  }

  if (discountType === 'fixed') {
    return discountValue;
  }
  return 0;
}

export function getMinimumPurchaseRequirementAmount({
  requirementType,
  subtotal,
  cartQuantity,
  requirementValue = 0,
}) {
  if (requirementType === 'cart_value') {
    return parseFloat(requirementValue) - parseFloat(subtotal);
  }
  if (requirementType === 'num_items' || requirementType === 'cart_quantity') {
    return parseFloat(requirementValue) - parseFloat(cartQuantity);
  }
  return 0;
}

export function getMinimumPurchaseRequirementMessage({
  requirementType,
  requiredValue,
  inactiveMessage = '',
}) {
  if (!isEmpty(inactiveMessage)) {
    return parse({
      template: {
        message: inactiveMessage,
      },
      data: {
        requiredValue,
        requirementType,
      },
    })?.message;
  } else if (requirementType === 'cart_value') {
    return `Add ${currencyHelper(requiredValue)} for this discount`;
  } else if (requirementType === 'num_items') {
    return `Add ${requiredValue} more products for this discount`;
  }
  return '';
}

export function getTagRequirementMessage({
  requiredTags,
  lineItems = [],
  inactiveMessage = '',
}) {
  const requiredTagsArray = requiredTags.map((tag) => tag?.value);
  const allTags = lineItems.reduce((acc, item) => {
    const tags = get(item, 'node.variant.product.tags', []);
    return [...acc, ...tags];
  }, []);
  const isAllTagsAvailable = requiredTagsArray.every((tag) =>
    allTags.includes(tag),
  );
  if (!isAllTagsAvailable) {
    return isEmpty(inactiveMessage)
      ? 'Current cart items does not qualify for this discount'
      : inactiveMessage;
  } else {
    return false;
  }
}

export function checkCouponTagRequirement(
  isValid = { valid: true, message: '' },
  { coupon, lineItems },
) {
  let message = isValid.message || '';
  let valid = isValid.valid;
  if (valid && coupon?.product_tags?.length > 0 && lineItems?.length > 0) {
    const tagRequirementError = getTagRequirementMessage({
      requiredTags: coupon?.product_tags,
      lineItems,
      inactiveMessage: coupon.inactive_message || '',
    });
    if (tagRequirementError) {
      valid = false;
      message = tagRequirementError;
    }
  }
  return {
    valid,
    message,
  };
}

export function checkCouponMinimumPurchaseRequirement(
  isValid = { valid: true, message: '' },
  { coupon, cartSubTotalAmount, cartTotalQuantity },
) {
  let message = isValid.message || '';
  let valid = isValid.valid;
  if (valid && coupon?.purchase_requirements) {
    let calculatedRequirementValue = getMinimumPurchaseRequirementAmount({
      requirementType: coupon.purchase_requirements,
      subtotal: cartSubTotalAmount,
      cartQuantity: cartTotalQuantity,
      requirementValue: coupon.minimum_purchase_requirements_value,
    });

    if (calculatedRequirementValue > 0) {
      valid = false;
      message = getMinimumPurchaseRequirementMessage({
        requiredValue: calculatedRequirementValue,
        requirementType: coupon.purchase_requirements,
        inactiveMessage: coupon.inactive_message || '',
      });
    }
  }
  return {
    valid,
    message,
  };
}

export function sortCoupons({ couponList, sortMethod }) {
  // DiscountValue in descending order
  if (sortMethod === 'discountValue') {
    const sortedCoupons = couponList?.sort?.((a, b) => {
      if (a.isValid && !b.isValid) {
        return -1;
      }
      if (!a.isValid && b.isValid) {
        return 1;
      }
      return (
        parseInt(b.calculatedSavingsValue) - parseInt(a.calculatedSavingsValue)
      );
    });
    return sortedCoupons;
  }
  return couponList;
}
