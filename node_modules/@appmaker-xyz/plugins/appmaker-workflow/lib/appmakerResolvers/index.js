import * as Flowed from '@appmaker/workflow/flowed';
import { DeleteScheduledPush } from './DeleteScheduledPush';
import ReadAppStorage from './ReadAppStorage';
import RunAction from './RunAction';
import { RunDataSource } from './RunDataSource';

import { SchedulePush } from './SchedulePush';
const { FlowManager } = Flowed;

const customResolverPlugin = {
  resolverLibrary: {
    'appmaker::SchedulePush': SchedulePush,
    'appmaker::RunDataSource': RunDataSource,
    'appmaker::ReadAppStorage': ReadAppStorage,
    'appmaker::RunAction': RunAction,
    'appmaker::DeleteScheduledPush': DeleteScheduledPush,
  },
};
export function registerCustomResolvers() {
  FlowManager.installPlugin(customResolverPlugin);
}
