const workFlowResponse = {
  triggers: [
    {
      trigger: 'cart.added',
      workflowsId: 'schedule_push_1hr',
      params: {},
    },
    {
      trigger: 'orderComplete',
      workflowsId: 'checkout_complete',
      params: {},
    },
  ],
  workflows: {
    schedule_push_1hr: {
      name: 'Schedule Cart abandoned',
      // steps: ,
      params: {
        tasks: {
          schedulePush: {
            requires: ['cart'],
            provides: ['localPushId'],
            resolver: {
              name: 'appmaker::SchedulePush',
              params: {
                pushAction: {
                  value: JSON.stringify({
                    action: 'RUN_WORKFLOW',
                    workflowId: 'on_push_click',
                  }),
                },
                pushLocalIdKey: {
                  value: 'cart_message_1_hr',
                },
                triggerInSeconds: {
                  value: 60,
                },
                title: {
                  transform:
                    'Checkout Now {{cart.lines.edges.length}} items in your cart',
                },
              },
              results: {
                localPushId: 'localPushId',
              },
            },
          },
          sendEventToFirebase: {},
        },
      },
    },
    on_push_click: {
      name: 'On push click',
      params: {
        tasks: {
          update_cart_attributes: {
            resolver: {
              name: 'appmakerShopify::UpdateCartAttributes',
              params: {
                attributes: {
                  value: [
                    {
                      key: 'appmaker_cart_abandoned_trigger_click',
                      value: 'w1',
                    },
                  ],
                },
              },
            },
          },
          open_cart: {
            resolver: {
              name: 'appmaker::RunAction',
              params: {
                action: {
                  value: 'OPEN_CART',
                },
              },
            },
          },
        },
      },
    },
    checkout_complete: {
      name: 'Checkout Complete',
      params: {
        tasks: {
          delete_schdeuled_push: {
            resolver: {
              name: 'appmaker::DeleteScheduledPush',
              params: {
                localPushId: {
                  value: 'cart_message_1_hr',
                },
              },
            },
          },
        },
      },
    },
    playground: {
      name: 'Checkout Complete',
      params: {
        tasks: {
          update_cart_attributes: {
            resolver: {
              name: 'appmakerShopify::UpdateCartAttributes',
              params: {
                attributes: {
                  value: [
                    {
                      key: 'appmaker_cart_abandoned_trigger_click_new',
                      value: 'saleeh',
                    },
                  ],
                },
              },
            },
          },
        },
      },
    },
  },
};

export { workFlowResponse };
