import { projectId } from '@appmaker-xyz/core';
import axios from 'axios';
import AsyncStorage from '@react-native-community/async-storage';
const workflowrespones_key = 'appmaker_workflow_response';
function saveWorkflowResponse(response) {
  AsyncStorage.setItem(workflowrespones_key, JSON.stringify(response.data));
}
export async function getWorkFlows() {
  const localData = await AsyncStorage.getItem(workflowrespones_key);
  if (localData) {
    axios
      .get(
        `https://app-frontend-api.appmaker.xyz/v1/${projectId}/workflows?API_KEY=08ac4d2d86a2d6d5cf41b4fcc1967a67`,
      )
      .then(saveWorkflowResponse);
    return JSON.parse(localData);
  } else {
    const response = await axios.get(
      `https://app-frontend-api.appmaker.xyz/v1/${projectId}/workflows?API_KEY=08ac4d2d86a2d6d5cf41b4fcc1967a67`,
    );
    saveWorkflowResponse(response);
    return response.data;
  }
}
