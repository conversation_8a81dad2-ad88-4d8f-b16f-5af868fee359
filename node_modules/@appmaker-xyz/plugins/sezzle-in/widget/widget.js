import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Modal,
  ImageBackground,
  ScrollView,
  ViewStyle,
  TextStyle,
  Linking,
} from 'react-native';
import styles from './styles';
import Plan from './component/plan';

// interface Props {
//   totalPrice: number;
//   containerStyle?: ViewStyle;
//   textStyle?: TextStyle;
//   logoTheme: string;
//   merchant_uuid: string;
// }

const SezzleWidget = ({
  totalPrice,
  containerStyle,
  logoTheme = 'color',
  merchant_uuid,
  textStyle,
}) => {
  const [modalVisibility, setModalVisiblitty] = useState(false);
  const [installmentInterval, setInstallmentInterval] = useState(0);
  const [maxPrice, setMaxPrice] = useState(8000);
  const [promotionText, setPromotionText] = useState('');

  useEffect(() => {
    fetch(`https://widget.sezzle.in/v2/config/${merchant_uuid}`)
      .then(response => response.json())
      .then(data => {
        setMaxPrice(data.maxPrice);
        if (data.promotion && data.promotion?.text !== undefined) {
          setPromotionText(data.promotion.text);
        } else {
          setPromotionText('');
        }
        if (data.isDynamicModal === true) {
          if (totalPrice < data.priceLimit) {
            setInstallmentInterval(14);
          } else {
            setInstallmentInterval(30);
          }
        } else {
          if (data.modalConfig !== undefined) {
            setInstallmentInterval(data.modalConfig.installmentIntervalInDays);
          } else {
            setInstallmentInterval(14);
          }
        }
      })
      .catch(error => {
        console.error(error);
      });
  }, []);

  const onLearnMoreClick = () => {
    Linking.canOpenURL(
      'https://help.sezzle.in/support/solutions/81000185067',
    ).then(supported => {
      if (supported) {
        Linking.openURL('https://help.sezzle.in/support/solutions/81000185067');
      } else {
        console.log("Don't know how to open URI: " + 'www.google.com');
      }
    });
  };

  if (installmentInterval === 0 || totalPrice > maxPrice) {
    return null;
  }

  return (
    <View style={containerStyle}>
      <Text style={[styles.text, textStyle]}>
        or 4 interest-free payments of
        <Text style={styles.price}> ₹{totalPrice / 4} </Text>
        with
        <TouchableOpacity
          onPress={() => setModalVisiblitty(true)}
          style={styles.touchableLogo}>
          {logoTheme === 'dark' ? (
            <Image
              style={styles.logo}
              source={require('./assets/darkLogo.png')}
            />
          ) : (
            <Image
              style={styles.logo}
              source={require('./assets/colorLogo.png')}
            />
          )}
          <Image
            style={styles.infoIcon}
            source={require('./assets/info.png')}
          />
        </TouchableOpacity>
      </Text>
      {promotionText !== '' && (
        <Text style={styles.promotionText}>{promotionText}</Text>
      )}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisibility}
        onRequestClose={() => setModalVisiblitty(false)}>
        <ScrollView contentContainerStyle={styles.modalScrollContainer}>
          <ImageBackground
            source={require('./assets/bg.png')}
            resizeMode={'stretch'}
            imageStyle={styles.modalImageBG}
            style={styles.modalBG}>
            <TouchableOpacity
              onPress={() => setModalVisiblitty(false)}
              style={styles.closeButton}>
              <Text style={styles.close}>X</Text>
            </TouchableOpacity>
            <Image
              style={styles.modalLogo}
              source={require('./assets/dark.png')}
            />
            <Text style={styles.modalHeading}>
              Sezzle it now. Pay us back later
            </Text>
            <Plan planIntervel={installmentInterval} />
            <Text style={styles.details}>
              No Interest, Ever{'\n'} Plus no fees if you pay on time
            </Text>
            <Text style={styles.details}>
              No Impact to Your {'\n'} Credit Score
            </Text>
            <Text style={styles.details}>Instant Approval{'\n'}Decisions</Text>
            <Text style={styles.lastText}>
              Just select "Sezzle" at checkout!
            </Text>
            <TouchableOpacity
              onPress={onLearnMoreClick}
              style={styles.learnMore}>
              <Text>learn more</Text>
            </TouchableOpacity>
          </ImageBackground>
        </ScrollView>
      </Modal>
    </View>
  );
};

export default SezzleWidget;
