import { simpleLocalStore as store } from '@appmaker-xyz/react-native';

// recently viewed items are stored in local storage
function getRecentlyViewedItems() {
  return JSON.parse(localStorage.getItem('recentlyViewedItems'));
}
function addRecentlyViewedItem(item) {
  const recentlyViewedItems = getRecentlyViewedItems();
  if (!recentlyViewedItems) {
    localStorage.setItem('recentlyViewedItems', JSON.stringify([item]));
  } else {
    const index = recentlyViewedItems.findIndex(
      (recentlyViewedItem) => recentlyViewedItem.id === item.id,
    );
    if (index === -1) {
      recentlyViewedItems.push(item);
    } else {
      recentlyViewedItems.splice(index, 1);
      recentlyViewedItems.push(item);
    }
    localStorage.setItem(
      'recentlyViewedItems',
      JSON.stringify(recentlyViewedItems),
    );
  }
}

class RecentlyViewdLocalStorage {
  constructor(config) {
    this.config = config;
    this.key = config.key;
    this.saveLimit = parseInt(config.saveLimit) || 5;
  }

  async saveItem({ id, item }) {
    try {
      const products = (await store.get(this.key)) || [];
      if (products.length >= this.saveLimit) {
        products.pop();
      }
      const index = products.findIndex(
        (recentlyViewedItem) => recentlyViewedItem.node.id === item.node.id,
      );
      if (index !== -1) {
        products.splice(index, 1);
      }
      products.unshift(item);
      await store.save(this.key, products);
      return products;
    } catch (error) {
      console.log('RecentlyViewdLocalStorage error', error);
    }
  }
  async deleteAllItems() {
    store.delete(this.key);
  }
  async removeItem({ id }) {
    const items = await store.get(this.key);
    delete items[id];
    await store.save(this.key, items);
    return items;
  }
  async allItems() {
    const items = (await store.get(this.key)) || [];
    return Object.values(items);
  }
}

export default RecentlyViewdLocalStorage;
