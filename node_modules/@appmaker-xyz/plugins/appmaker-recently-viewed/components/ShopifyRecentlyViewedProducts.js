import React from 'react';
import { BlockCard } from '@appmaker-xyz/uikit';
import { appPluginStoreApi, applyFilters } from '@appmaker-xyz/core';
import { useRecentlyViewedStore } from '../recentlyViewedStore';
import { Platform } from 'react-native';

export const ShopifyRecentlyViewedProducts = ({
  attributes,
  BlocksView,
  innerBlocks,
  ...props
}) => {
  const settings =
    appPluginStoreApi()?.getState()?.plugins['appmaker-recently-viewed']
      ?.settings;
  const ids = useRecentlyViewedStore().getRecentlyViewed();
  const pagesData = {
    blocks: [
      {
        attributes: {
          customDataSource: {
            source: 'shopify',
            attributes: {
              mapping: {
                items: 'data.data.products.edges',
              },
              methodName: 'products',
              params: {
                ids: ids,
                productsLimit: 10,
              },
            },
            repeatable: 'Yes',
            repeatItem: 'DataSource',
          },
          numberOfItemsToShowInScroller:
            "{{plugins['appmaker-recently-viewed'].settings.numberOfItemsToShowInScroller}}",
          horizontal: true,
          surface: 'recently-viewed-product-scroller',
          gridViewListing: false,
          hasPages: false,
          ...attributes,
        },
        name: 'appmaker/shopify-product-list',
        innerBlocks: [],
      },
    ],
  };
  const allStyles = applyFilters(
    'appmaker-recently-viewed-product-scroller-pdp-styles',
    {
      childContainerStyle: {
        paddingBottom: 12,
      },
      wholeContainerStyle: {},
    },
  );

  if (ids.length === 0) {
    return null;
  }

  if (settings?.hide_title_block_of_recently_viewed_in_pdp) {
    return (
      <BlocksView inAppPage={pagesData} {...props} blockData={props.data} />
    );
  }

  return (
    <BlockCard
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        ...allStyles,
      }}
      {...props}>
      <BlocksView inAppPage={pagesData} {...props} blockData={props.data} />
    </BlockCard>
  );
};
