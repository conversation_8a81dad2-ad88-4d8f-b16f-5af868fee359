import React, { useState } from 'react';
import { StyleSheet } from 'react-native';

import { Layout } from '@appmaker-xyz/uikit';
import WebView from 'react-native-webview';
import Modal from 'react-native-modal';
// import { AppmakerText, Button } from '../../index';
import Icon from 'react-native-vector-icons/Feather';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { usePageState } from '@appmaker-xyz/core';
import { useApThemeState } from '@appmaker-xyz/uikit/src/index';
import { AppmakerText, Button } from '@appmaker-xyz/uikit/src/components/index';
import { usePluginStore } from '@appmaker-xyz/core';
function formatDataForProduct(jsonData) {
  const formated = {};
  Object.keys(jsonData).forEach((key) => {
    if (jsonData[key] === '' || !key.match(/properties/)) {
    } else {
      const newKey = key.replace('properties[', '').replace(']', '');
      formated[newKey] = jsonData[key];
    }
  });
  return formated;
}
function getBookingUrl({ shopId, productId, variantId }) {
  const BASE_URL = 'https://app-static-pages.appmaker.xyz/shopify/sesami/';
  return `${BASE_URL}?shopId=${shopId}&productId=${productId}&variantId=${variantId}`;
}
const TimeSlotBooking = ({ attributes, onPress, onAction }) => {
  const [timeSlot, setTimeSlot] = useState('');
  const [visible, setVisibility] = useState(false);
  const open = () => setVisibility(true);
  const close = () => setVisibility(false);
  const { appmakerAction } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const setpageState = usePageState((state) => state.setState);
  const onMessage = (event) => {
    const { data } = event.nativeEvent;

    const jsonData = JSON.parse(data);
    if (jsonData['properties[Date]']) {
      setTimeSlot(
        `${jsonData['properties[Date]']} Time ${jsonData['properties[Time]']}`,
      );
    }
    setVisibility(false);
    const productAttributes = formatDataForProduct(jsonData);
    setpageState((state) => {
      if (!state.productAttributes) {
        state.productAttributes = {};
      }
      state.productAttributes = {
        ...state.productAttributes,
        ...productAttributes,
      };
    });
  };
  const { productId, variantId } = attributes;
  const shopId = usePluginStore(
    (state) => state.plugins['sesami-booking'].settings.shopId,
  );
  let normalProductId = '',
    normalVariantId = '';
  try {
    normalProductId = shopifyIdHelper(productId, true);
    normalVariantId = shopifyIdHelper(variantId, true);
  } catch (error) {
    console.log(error);
  }
  console.log('timeSlot', timeSlot);
  return attributes?.tags.match('sesami-service') ? (
    <Layout style={styles.container}>
      <Layout style={styles.barContainer}>
        <AppmakerText category="actionTitle" style={styles.title}>
          Appointment Time
        </AppmakerText>
        <Layout style={styles.barContainer}>
          {timeSlot ? (
            <AppmakerText category="bodyParagraphBold" style={styles.timeText}>
              {timeSlot.replace(/:/, ' ')}
            </AppmakerText>
          ) : (
            <Button onPress={open} small status="dark">
              Pick a time
            </Button>
          )}
          {timeSlot ? (
            <Icon
              name="x"
              size={12}
              onPress={() => setTimeSlot('')}
              style={styles.icon}
            />
          ) : null}
        </Layout>
      </Layout>
      <Modal
        testID={'modal'}
        isVisible={visible}
        onSwipeComplete={close}
        onBackButtonPress={close}
        backdropTransitionOutTiming={0}
        onBackdropPress={close}
        style={styles.view}>
        <WebView
          source={{
            uri: getBookingUrl({
              shopId,
              productId: normalProductId,
              variantId: normalVariantId,
            }),
          }}
          style={{ width: '100%' }}
          onMessage={onMessage}
        />
      </Modal>
    </Layout>
  ) : null;
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.base,
      flex: 1,
      marginBottom: spacing.nano,
    },
    barContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    title: { flex: 1 },
    view: {
      borderRadius: spacing.small,
      overflow: 'hidden',
    },
    icon: {
      marginLeft: spacing.nano,
      padding: spacing.mini,
    },
    timeText: {
      backgroundColor: color.light,
      padding: spacing.nano,
      borderRadius: spacing.nano,
    },
  });

export default TimeSlotBooking;
