import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useAction } from '@appmaker-xyz/react-native';
import Google from '../img/google.svg';
import { Layout, ThemeText, AppTouchable } from '@appmaker-xyz/ui';
import { usePageState } from '@appmaker-xyz/core';

export default function GoogleLoginButton() {
  const googleLoginLoading = usePageState((state) => state.googleLoginLoading);
  const [buttonLoading, setButtonLoading] = useState(false);
  useEffect(() => {
    setButtonLoading(googleLoginLoading);
  }, [googleLoginLoading]);
  const onAction = useAction();

  return (
    <AppTouchable
      style={styles.socialButton}
      onPress={() => {
        console.log('google login button pressed');
        onAction({
          action: 'LOGIN_USER_VIA_GOOGLE',
        });
      }}>
      {buttonLoading ? (
        <ActivityIndicator size={'small'} color={'#4285F4'} />
      ) : (
        <Google height={20} width={20} />
      )}
    </AppTouchable>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  line: {
    height: 1,
    backgroundColor: '#E8ECF4',
    flex: 1,
    marginHorizontal: 8,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E8ECF4',
    borderRadius: 6,
    paddingVertical: 12,
  },
});
