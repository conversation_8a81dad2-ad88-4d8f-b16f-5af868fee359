import { projectId } from '@appmaker-xyz/core';
import axios from 'axios';

const url = `https://otp-login-service.appmaker.xyz/v2/project/${projectId}/otp-login`;
export async function otpMultipass({ token, provider }) {
  const data = {
    firebase_token: token,
    provider,
  };
  const response = await axios.post(url, data);
  return response.data;
}

export async function otpMultipassDebug({ phone }) {
  const data = {
    debug: true,
    debug_value: { phone },
  };
  const response = await axios.post(
    url,
    // 'https://ate-heritage-tires-childrens.trycloudflare.com/v1/project/144498/otp-login',
    // `https://shipping-mandatory-submission-southwest.trycloudflare.com/v1/project/${projectId}/otp-login`,
    data,
  );
  return response.data;
}

export async function otpMultipassLinkAccount({
  phone,
  email,
  first_name,
  last_name,
  debug,
}) {
  const data = {
    debug: true,
    debug_value: { phone },
    email,
    first_name,
    last_name,
  };
  const response = await axios.post(
    url,
    // 'https://ate-heritage-tires-childrens.trycloudflare.com/v1/project/144498/otp-login',
    // `https://shipping-mandatory-submission-southwest.trycloudflare.com/v1/project/${projectId}/otp-login`,
    data,
  );
  return response.data;
}
