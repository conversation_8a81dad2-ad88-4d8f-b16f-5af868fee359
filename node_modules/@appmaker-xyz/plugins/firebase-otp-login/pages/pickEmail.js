import { color, spacing } from '@appmaker-xyz/uikit/src/styles/index';

const pickEmail = {
  type: 'normal',
  title: 'Shipping Method',
  attributes: {
    renderType: 'normal',
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/text',
      attributes: {
        content:
          'We have found multiple email address for this phone number. Please select one. or Create a new one',
        category: 'bodyParagraph',
        status: 'demiDark',
        style: {
          padding: spacing.base,
        },
      },
    },
    {
      name: 'appmaker/form-radio-item',
      attributes: {
        name: 'user_email',
        id: '{{blockItem.email}}',
        label: '{{blockItem.email}}',
        value: '{{blockItem}}',
        // value_idKey:'email'
        dataSource: {
          responseType: 'replace',
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData.response.profile_list}}',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
      },
    },
    {
      name: 'appmaker/form-radio-item',
      attributes: {
        name: 'user_email',
        id: 'custom_email',
        label: 'New Email',
        value: 'custom_email',
      },
    },
  ],
  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/button',
    attributes: {
      appmakerAction: {
        action: 'SET_USER_OTP_FIREBASE_EMAIL',
      },
      content: 'Continue',
      wholeContainerStyle: {
        borderRadius: 0,
      },
      status: 'dark',
    },
  },
};

export default pickEmail;
