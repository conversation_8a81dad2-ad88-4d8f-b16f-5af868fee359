import dayjs from 'dayjs';

import { isCartExpired } from '../index';

describe('Cart clear cases', () => {
  test('Case 0: if createdAt is undefined and clear_cart_by_date is true then cart should clear', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '2',
      clear_cart_by_date: true,
      cart_expire_date: '2022-11-28T00:30:00+05:30',
    };
    const checkout = {
      id: '2',
      // createdAt: ,
    };
    expect(isCartExpired({ settings, checkout })).toBeTruthy();
  });

  test('Case 1: if createdAt is undefined and clear_cart_by_date is false then cart should not clear', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '2',
      clear_cart_by_date: false,
      cart_expire_date: '2022-11-28T00:30:00+05:30',
    };
    const checkout = {
      id: '2',
      // createdAt: ,
    };
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });

  test('Case 2', () => {
    const settings = {
      can_cart_expire: true,
      cart_expire_days: '2',
      clear_cart_by_date: false,
      cart_expire_date: '2022-11-28T00:30:00+05:30',
    };
    const checkout = {
      id: '2',
      createdAt: '2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeTruthy();
  });

  test('Case 3', () => {
    const settings = {
      can_cart_expire: true,
      cart_expire_days: '9',
      clear_cart_by_date: false,
      cart_expire_date: '2022-11-28T00:30:00+05:30',
    };
    const checkout = {
      id: '2',
      createdAt: dayjs().subtract(7, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });

  test('Case 4', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '9',
      clear_cart_by_date: true,
      cart_expire_date: dayjs(),
    };
    const checkout = {
      id: '2',
      createdAt: dayjs().subtract(7, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeTruthy();
  });

  test('Case 5', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '9',
      clear_cart_by_date: true,
      cart_expire_date: dayjs(),
    };
    const checkout = {
      id: '2',
      createdAt: dayjs().subtract(1, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeTruthy();
  });

  test('Case 6', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '9',
      clear_cart_by_date: true,
      cart_expire_date: dayjs(),
    };
    const checkout = {
      id: '2',
      createdAt: dayjs().add(1, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });

  test('Case 7: null case', () => {
    const settings = {};
    const checkout = {
      id: '2',
      createdAt: dayjs().add(1, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });
  test('Case 8: null case', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '9',
      clear_cart_by_date: true,
      cart_expire_date: dayjs(),
    };
    const checkout = {};
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });

  test('Case 9', () => {
    const settings = {
      can_cart_expire: false,
      cart_expire_days: '9',
      clear_cart_by_date: true,
      cart_expire_date: dayjs(),
    };
    const checkout = {
      id: '2',
      createdAt: dayjs().add(1, 'day'), //'2022-11-24T00:30:00+05:30',
    };
    expect(isCartExpired({ settings, checkout })).toBeFalsy();
  });
});
