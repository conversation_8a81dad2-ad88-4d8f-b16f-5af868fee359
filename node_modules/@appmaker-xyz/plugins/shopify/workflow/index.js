// import * as Flowed from '@appmaker/workflow/flowed';
import { analytics } from '@appmaker-xyz/react-native';
async function UpdateCartAttributes(_params, context) {
  const { actionContext } = context;
  const { runDataSource, appStorageState } = actionContext;
  const { attributes } = _params;
  const checkoutId = appStorageState.checkout.id;
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  try {
    const [checkoutDataResponse] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'checkoutById',
        params: checkoutId,
      },
    );
    const cartData = checkoutDataResponse.data.data.checkout;
    const cartAttributes = cartData.attributes;
    const newAttributes = [...cartAttributes, ...attributes];
    const [updateCartResponse] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: 'cartSetAttributes',
        params: {
          checkoutId,
          attributes: newAttributes,
        },
      },
    );
  } catch (error) {
    console.log(error, 'error');
  }
  return {};
}
async function firebaseLogevent(_params, context) {
  const { eventName, params } = _params;
  await analytics().logEvent(eventName, params);
}
async function firebaseSetUserId(_params, context) {
  const { id } = _params;
  await analytics().setUserId(id);
}

async function firebaseSetUserProperty(_params, context) {
  const { name, value } = _params;
  analytics().setUserProperty(name, value);
}

const customResolverPlugin = {
  resolverLibrary: {
    'appmakerShopify::UpdateCartAttributes': UpdateCartAttributes,
    'firebase::LogEvent': firebaseLogevent,
    'firebase::setUserId': firebaseSetUserId,
    'firebase::setUserProperty': firebaseSetUserProperty,
  },
};
export function registerCustomResolvers() {
  // Flowed.FlowManager.installPlugin(customResolverPlugin);
}
