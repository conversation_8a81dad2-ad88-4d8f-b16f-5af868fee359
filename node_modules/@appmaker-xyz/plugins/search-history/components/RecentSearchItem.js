import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

const RecentSearchItem = (props) => {
  const { attributes, onAction } = props;

  return (
    <TouchableOpacity
      style={styles.wholeContainer}
      onPress={() => {
        onAction &&
          onAction({
            action: 'LIST_PRODUCT',
            params: {
              searchResult: true,
              title: attributes?.query,
              searchQuery: attributes?.query,
              search: attributes?.query,
            },
          });
      }}>
      <View style={styles.container}>
        <Icon name="clock" size={18} color="#9CA3AF" style={styles.icon} />
        <Text style={styles.text}>{attributes?.query}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  wholeContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 6,
  },
  text: {
    fontSize: 16,
  },
});

export default RecentSearchItem;
