import React, { useState } from 'react';
import { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getAllSearchQuery } from '../helper/index';
const RecentSearchContainer = (props) => {
  const { BlockItem, BlocksView, onAction, attributes = {} } = props;
  const { title } = attributes;
  const [recentQueries, setRecentQueries] = useState([]);
  useEffect(() => {
    const getRecentQueries = async () => {
      const data = await getAllSearchQuery();
      if (data?.length > 0) {
        setRecentQueries(data);
      }
    };
    getRecentQueries();
  }, []);
  const blockTitle = title ? title : 'Your past searches';
  return recentQueries?.length > 0 ? (
    <View style={styles.container}>
      <Text style={styles.title}>{blockTitle}</Text>
      {recentQueries?.map((query) => (
        <BlockItem
          BlockItem={BlockItem}
          BlocksView={BlocksView}
          // currentAction={currentAction}
          onAction={onAction}
          block={{
            name: 'appmaker/recent-search-item',
            // innerBlocks,
            clientId: 'review-item',
            isValid: true,
            attributes: {
              query: query,
            },
          }}
        />
      ))}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
  },
  title: {
    paddingHorizontal: 12,
    paddingTop: 8,
    paddingBottom: 6,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default RecentSearchContainer;
