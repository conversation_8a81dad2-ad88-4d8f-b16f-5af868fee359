import { appmaker } from '@appmaker-xyz/core';
import RecentSearchContainer from '../components/RecentSearchContainer';
import RecentSearchItem from '../components/RecentSearchItem';

const registerBlocks = () => {
  appmaker.blocks.registerBlockType({
    name: 'appmaker/recent-search-wrapper',
    View: RecentSearchContainer,
  });
  appmaker.blocks.registerBlockType({
    name: 'appmaker/recent-search-item',
    View: RecentSearchItem,
  });
};

export { registerBlocks };
