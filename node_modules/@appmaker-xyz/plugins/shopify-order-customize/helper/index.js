import { parse } from '@appmaker-xyz/core';
import { addFilter } from '@appmaker-xyz/core';

const activateFilters = (settings) => {
  addFilter(
    'shopify-order-track-url',
    'shopify-order-track-url-order-customize',
    (defaultUrl, { order }) => {
      const baseUrl = settings?.track_url;
      const data = order;
      let url = parseText?.(baseUrl, data);
      return url?.message || defaultUrl;
    },
  );
};

const parseText = (text, data) => {
  if (text && typeof text === 'string') {
    const cleanLabel = parse?.({
      template: {
        message: text,
      },
      data: {
        order: data,
      },
    });
    return cleanLabel;
  }
  return { message: text };
};

export { activateFilters };
