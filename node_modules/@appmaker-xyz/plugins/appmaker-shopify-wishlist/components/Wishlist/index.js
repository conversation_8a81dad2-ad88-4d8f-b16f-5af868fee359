import React from 'react';

export const Wishlist = ({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) => {
  const defaultDataSource = {
    source: 'appmaker-shopify-wishlist',
    attributes: {
      mapping: {
        items: 'data.data.products.edges',
      },
      methodName: 'getAllProducts',
      params: {
        key: 'default',
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  const finalAttributes = {
    loadingLayout: 'product-grid',
    wishList: true,
    id: '{{blockItem.node.id}}',
    hasPages: false,
    appmakerAction: {
      pageId: 'productDetail',
      params: {
        pageData: '{{blockItem}}',
      },
      action: 'OPEN_PRODUCT_DETAIL',
    },
    // cartQuantity: '{{getCartQty(appStorageState.checkout,blockItem.node.id)}}',
    title: '{{blockItem.node.title }}',
    numColumnsDefault: 2,
    gridViewListing: true,
    uri: '{{blockItem.node.images.edges[0].node.url}}',
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    in_stock:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    blockItem: '{{blockItem}}',
    titleNumberOfLines:
      '{{checkIfTrueFalse(plugins.shopify.settings.control_number_of_lines_in_product_list) == true ? (plugins.shopify.settings.number_of_lines_in_product_list) : "" }}',
    availableForSale:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    regularPrice:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %> <% if( regularPrice>salePrice) { %> {{currencyHelper(regularPrice,blockItem.node.compareAtPriceRange.minVariantPrice.currencyCode)}}<% } %>',
    salePrice:
      '{{ currencyHelper(blockItem.node.priceRange.minVariantPrice.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
    onSale:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><%=echo(regularPrice>salePrice)%>',
    salePercentage:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><% if( regularPrice>salePrice) { %><%= parseInt(100-((salePrice/regularPrice)*100 ))%> <%="%"%>%><% } %>',
    thumbnail_meta: {
      height: '{{blockItem.node.images.edges[0].node.height}}',
      width: '{{blockItem.node.images.edges[0].node.width}}',
    },
    bestSeller: '{{blockItem.node.bestseller_tag}}',
    brandName:
      '<% if(checkIfTrueFalse(plugins.shopify.settings.show_vendor_pdp) == true){ %><%=blockItem.node.vendor%><% } %>',
    new_product: '{{blockItem.node.new_tag}}',
    brandColor: { primary: '#B6DCC9', secondary: '#E63F12' },
    saleBadgeStyle: {
      textStyle: {
        color: 'white',
      },
      containerStyle: {
        backgroundColor: '#E63F12',
      },
    },
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    show_last_few_remaining:
      '{{ blockItem.node.totalInventory < 5 && blockItem.node.totalInventory > 0 }}',
    last_few_remaining_text: 'Last Few Remaining',
    ...(attributes && attributes),
    dataSource: attributes.customDataSource
      ? attributes.customDataSource
      : defaultDataSource,
  };
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'appmaker/product-grid-item',
        innerBlocks,
        clientId: 'product-list',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
}


export default Wishlist;
