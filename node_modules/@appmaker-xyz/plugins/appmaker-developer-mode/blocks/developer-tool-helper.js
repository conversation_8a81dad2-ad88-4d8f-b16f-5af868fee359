import { useAppState, useAppStorage, usePageState } from '@appmaker-xyz/core';
import React, { useState } from 'react';
import {
  View,
  Text,
  Button,
  Modal,
  StyleSheet,
  ScrollView,
} from 'react-native';
import JSONTree from 'react-native-json-tree';

function ViewItem({ data, title }) {
  const rawData = JSON.parse(JSON.stringify(data));
  return (
    <View style={styles.dataContainer}>
      <Text style={styles.dataTitle}>{title}</Text>
      <JSONTree data={rawData} />
    </View>
  );
}

export default function DeveloperTool(props) {
  const [modalVisible, setModalVisible] = useState(false);
  const pageState = usePageState();
  const appState = useAppState();
  const appStorageState = useAppStorage();

  return (
    <View style={styles.container}>
      <Button
        title="Open Developer Tool"
        onPress={() => setModalVisible(true)}
        style={styles.button}
      />

      <Modal visible={modalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Developer Tool</Text>
          <ScrollView style={styles.scrollView}>
            <ViewItem data={pageState} title="Page State" />
            <ViewItem data={appState} title="App State" />
            <ViewItem data={appStorageState} title="App Storage State" />
            <ViewItem data={props} title="Props" />
          </ScrollView>

          <Button title="Close" onPress={() => setModalVisible(false)} />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    padding: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  dataContainer: {
    marginBottom: 20,
  },
  dataTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  button: {
    position: 'absolute',
    bottom: 20,
    left: 20,
  },
});
