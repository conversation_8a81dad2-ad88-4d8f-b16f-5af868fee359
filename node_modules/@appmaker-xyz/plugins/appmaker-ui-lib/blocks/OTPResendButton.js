import React, { useState, useEffect } from 'react';
import { Button } from '@appmaker-xyz/uikit';

const ResendOTPButton = (props) => {
  const [time, setTime] = useState(60);
  const [disableButton, setDisableButton] = useState(true);
  // const [loading, setLoading] = useState(false);

  useEffect(() => {
    let interval;
    if (disableButton === true) {
      interval = setInterval(() => {
        setTime((t) => {
          if (t - 1 === 0) {
            clearInterval(interval);
            setDisableButton(false);
            return 60;
          }
          return t - 1;
        });
      }, 1000);
    }
    return () => interval && clearInterval(interval);
  }, [disableButton]);
  const onPress = async () => {
    if (props.onPress) await props.onPress();
    setDisableButton(true);
  };
  return (
    <Button
      disabled={disableButton}
      {...props}
      onPress={onPress}>{`Resend OTP ${
      time !== 60 ? `in ${time}s` : ''
    }`}</Button>
  );
};

export default ResendOTPButton;
