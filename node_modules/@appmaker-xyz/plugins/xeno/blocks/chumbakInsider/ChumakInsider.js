import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import { Layout, AppmakerText, AppImage } from '@appmaker-xyz/uikit';

const ChumakInsider = ({ attributes, onPress, pageDispatch, onAction }) => {
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <Layout style={styles.container}>
      <Layout style={styles.infoContainer}>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/popup_logo-removebg-preview_1024x1024.png"
          style={styles.image}
        />
        <Layout style={styles.insiderBadge}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <AppmakerText>BENEFITS</AppmakerText>
        </Layout>
        <Layout style={styles.content}>
          <AppmakerText category="bodyParagraphBold">
            Free Priority Shipping
          </AppmakerText>
          <AppmakerText category="highlighter1" status="demiDark">
            Enjoy priority shipping, free of any additional cost on every order
          </AppmakerText>
        </Layout>
        <Layout style={styles.content}>
          <AppmakerText category="bodyParagraphBold">
            Additional Discounts
          </AppmakerText>
          <AppmakerText category="highlighter1" status="demiDark">
            Enjoy a guaranteed 10% discount on all future purchases (over &
            above; no conditions)
          </AppmakerText>
        </Layout>
        <Layout style={styles.content}>
          <AppmakerText category="bodyParagraphBold">
            Exclusive Access
          </AppmakerText>
          <AppmakerText category="highlighter1" status="demiDark">
            Get special access to preview all upcoming sales and events.
          </AppmakerText>
        </Layout>
        <Layout style={styles.content}>
          <AppmakerText category="bodyParagraphBold">Gifts & More</AppmakerText>
          <AppmakerText category="highlighter1" status="demiDark">
            Get your Chumbak free gift on every 3rd purchase.
          </AppmakerText>
        </Layout>
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    container: {
      backgroundColor: '#B6DCC9',
    },
    infoContainer: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.md,
    },
    image: {
      width: 120,
      height: 80,
      resizeMode: 'contain',
      marginBottom: spacing.xl,
      alignSelf: 'center',
    },
    insiderBadge: {
      flexDirection: 'row',
      backgroundColor: color.white,
      alignSelf: 'flex-start',
      alignItems: 'center',
      padding: spacing.mini,
      marginBottom: spacing.base,
    },
    insiderImage: {
      width: 90,
      height: 30,
      resizeMode: 'contain',
      marginRight: spacing.mini,
    },
    content: {
      marginBottom: spacing.base,
    },
    ctaContainer: {
      backgroundColor: color.white,
      flex: 1,
      padding: spacing.base,
      alignItems: 'center',
      justifyContent: 'space-evenly',
    },
    becomeInsiderBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#B6DCC9',
      padding: spacing.mini,
      width: '100%',
      justifyContent: 'center',
    },
    options: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    },
    option: {
      borderColor: active ? color.dark : color.light,
      borderWidth: 1,
      paddingVertical: spacing.small,
      paddingHorizontal: spacing.base,
      alignItems: 'center',
      shadowColor: '#000',
    },
    button: {
      borderRadius: 0,
    },
  });

export default ChumakInsider;
