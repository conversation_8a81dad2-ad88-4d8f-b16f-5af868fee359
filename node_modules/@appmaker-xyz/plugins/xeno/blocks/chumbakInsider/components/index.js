import React from 'react';
import ChumbakInsiderButton from './InsiderButton';
import ChumbakInsiderCartButton from './InsiderCartButton';
import InsiderBadge from './InsiderBadge';

const InsiderButton = ({ attributes, onAction }) => {
  if (attributes.type === 'cart_button') {
    if (
      attributes.blockItem?.lineItems?.edges.filter(
        (i) => i.node?.variant?.product?.productType === `membership`,
      ).length > 0
    ) {
      return null;
    } else {
      return (
        <ChumbakInsiderCartButton
          attributes={{ ...attributes }}
          onAction={onAction}
        />
      );
    }
  } else if (attributes.type === 'price-badge') {
    return <InsiderBadge attributes={{ ...attributes }} />;
  }

  return (
    <ChumbakInsiderButton attributes={{ ...attributes }} onAction={onAction} />
  );
};

export default InsiderButton;
