import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import {
  AppmakerText,
  AppImage,
  AppTouchable,
  Layout,
} from '@appmaker-xyz/uikit';
import Icon from 'react-native-vector-icons/Feather';
import { useAppStorage } from '@appmaker-xyz/core';
import { usePluginStore } from '@appmaker-xyz/core';

const ChumbakInsiderButton = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;
  const startsWith = (arr, str) => {
    return arr.some((item) => item.startsWith(str));
  };
  const { settings } = usePluginStore((state) => state.plugins.xeno);
  const user = useAppStorage((state) => state.user);
  const insider =
    user && user?.tags?.length > 0
      ? startsWith(
          user.tags,
          settings?.userTagsForXenoStartsWith || 'Xeno Tier:Member',
        )
      : false;
  const insiderText = insider
    ? `Enjoy free shipping and extra ${settings.discountPercentageOnMembership}% off.`
    : `members get free shipping & ${settings.discountPercentageOnMembership}% off.`;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      console.log(appmakerAction);
      onAction(appmakerAction);
    };
  }

  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <>
      {insider ? (
        <Layout style={styles.membershipAddButton}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <AppmakerText category="bodySubText">{insiderText}</AppmakerText>
        </Layout>
      ) : null}
      {/* {insider == false ? (
        <AppTouchable style={styles.container} onPress={onPressHandle}>
          <Layout style={styles.membershipAddButton}>
            <AppImage
              uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
              style={styles.insiderImage}
            />
            <AppmakerText category="bodySubText">{insiderText}</AppmakerText>
          </Layout>
          <Icon name="plus" size={20} color={color.dark} style={styles.icon} />
        </AppTouchable>
      ) : (
        <Layout style={styles.membershipAddButton}>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <AppmakerText category="bodySubText">{insiderText}</AppmakerText>
        </Layout>
      )} */}
    </>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    membershipAddButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: spacing.base,
      backgroundColor: '#B6DCC9',
    },
    insiderImage: {
      width: 60,
      height: 30,
      resizeMode: 'contain',
      marginRight: spacing.mini,
    },
    icon: {
      paddingHorizontal: spacing.lg,
    },
  });

export default ChumbakInsiderButton;
