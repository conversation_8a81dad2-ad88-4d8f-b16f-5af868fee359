import React, { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import { AppmakerText, AppImage, AppTouchable, Layout } from '@appmaker-xyz/uikit';
import Icon from 'react-native-vector-icons/Feather';

const ChumbakInsiderCartButton = ({ attributes, onPress, onAction }) => {
  const { appmakerAction } = attributes;

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => {
      onAction(appmakerAction);
    };
  }

  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  return (
    <AppTouchable style={styles.container} onPress={onPressHandle}>
      <Layout style={styles.containerInner}>
        <AppmakerText>Add</AppmakerText>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
          style={styles.insiderImage}
        />
        <AppmakerText>membership</AppmakerText>
      </Layout>
      <Icon name="plus" size={20} color={color.dark} style={styles.icon} />
    </AppTouchable>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-evenly',
      padding: spacing.base,
      backgroundColor: '#B6DCC9',
      width: '100%',
    },
    containerInner: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    insiderImage: {
      width: 60,
      height: 30,
      resizeMode: 'contain',
      marginHorizontal: spacing.mini,
    },
    icon: {
      paddingHorizontal: spacing.lg,
    },
  });

export default ChumbakInsiderCartButton;
