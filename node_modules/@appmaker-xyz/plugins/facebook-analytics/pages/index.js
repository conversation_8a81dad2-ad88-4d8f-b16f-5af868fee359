const SamplePage = {
  id: 'checkout',
  status: 'active',
  title: 'Checkout',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        title: 'ACTION_TEST',
        appmakerAction: {
          action: 'ACTION_TEST',
          workflowId: 'playground',
        },
      },
    },
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        title: 'TRIGGER_LOCAL_NOTIFICATION',
        appmakerAction: {
          params: {},
          action: 'TRIGGER_LOCAL_NOTIFICATION',
        },
      },
    },
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        title: 'SEND_LOCAL_NOTIFICATION',
        appmakerAction: {
          params: {},
          action: 'SEND_LOCAL_NOTIFICATION',
        },
      },
    },
    {
      clientId: 'my-account-menu',
      name: 'appmaker/actionbar',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        title: 'Log out',
        appmakerAction: {
          params: {},
          action: 'LOGOUT',
        },
      },
    },
  ],
};
export default SamplePage;
