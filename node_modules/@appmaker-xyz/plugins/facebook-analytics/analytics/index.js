import { appmaker, analytics } from '@appmaker-xyz/core';
import { analytics as facebookAnalytics } from '@appmaker-xyz/react-native';
import { appStorageApi } from '@appmaker-xyz/core';
import { AppEventsLogger } from 'react-native-fbsdk-next';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { logEvent } from '../lib';

// export const logEvent = (key, value) => {
//   const store = appStorageApi().getState();
//   let data = { ...value };
//   if (store.user) {
//     const user = store.user;
//     data = { ...value, user_id: user.id };
//   }
//   // exapmle // Log standard event. e.g. completed registration
// };

export const logAddToWishList = async (product) => {
  const { id, title, priceRange } = product;
  const normalProductId = shopifyIdHelper(id, true);
  const { amount, currencyCode } = priceRange?.minVariantPrice;
  const price = parseFloat(amount);
  const params = {
    item_name: title,
    fb_content_type: 'product',
    fb_content_id: normalProductId,
    fb_description: title,
    fb_currency: currencyCode,
    value: price,
  };
  logEvent('fb_mobile_add_to_wishlist', price, params, product);
};

export const logPurchase = async ({ cart }) => {
  let totalAmount = parseFloat(cart.totalPrice?.amount);
  const contentIds = JSON.stringify(
    cart.lineItems.edges.map(({ node }) => shopifyIdHelper(node.id, true)),
  );
  const params = {
    fb_content_type: 'product',
    fb_currency: cart.totalPrice?.currencyCode,
    value: totalAmount,
    fb_content_id: contentIds,
    fb_num_items: cart?.lineItems?.edges?.length,
  };
  logEvent('fb_mobile_purchase', totalAmount, params, cart);
};
