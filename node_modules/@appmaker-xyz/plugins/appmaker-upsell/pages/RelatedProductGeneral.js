import React from 'react';
import { BlockCard } from '@appmaker-xyz/uikit';
import { useCart } from '@appmaker-xyz/shopify';
import { settings } from '../pluginSettings';
export const ShopifyRelatedProductScroller = ({
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  ...props
}) => {
  const { cart: cartData, lineItems } = useCart();
  const productId = lineItems?.[0]?.node?.variant?.product?.id;
  const limit = settings?.num_of_product_cart;
  let productLimit = 2;
  if (limit) {
    try {
      productLimit = parseInt(limit);
    } catch (e) {
      console.log('upsell limit parsing error');
    }
  }
  // Z2lkOi8vc2hvcGlmeS9Qcm9kdWN0LzE1MDc4NjI4NzIxNTc

  // ************************************************
  // ************************************************
  const pagesData = {
    blocks: [
      {
        dependencies: {
          pageState: ['metaData'],
        },
        attributes: {
          customDataSource: {
            source: 'shopify',
            attributes: {
              mapping: {
                items: 'data.data.productRecommendations',
              },
              methodName: 'productRecommendations',
              params: {
                limit:
                  productLimit && typeof productLimit === 'number'
                    ? productLimit
                    : 2,
                productId: productId,
                filterFn: (data) => {
                  if (cartData) {
                    const filteredData = data.filter((v) => {
                      let add = true;
                      cartData.lineItems.edges.map(({ node }) => {
                        if (node.variant.product.id === v.id) {
                          add = false;
                        }
                      });
                      return add;
                    });
                    return filteredData;
                  }
                  return data;
                },
              },
            },
            repeatable: 'Yes',
            repeatItem: 'DataSource',
          },
          surface: 'appmaker-upsell-product-scroller',
          hasPages: false,
          horizontal: true,
          gridViewListing: false,
          ...attributes,
        },
        name: 'appmaker/upsell-product-grid',
        innerBlocks: [],
      },
    ],
  };
  return (
    <BlockCard
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
      }}
      {...props}>
      <BlocksView inAppPage={pagesData} {...props} blockData={props.data} />
    </BlockCard>
  );
};
