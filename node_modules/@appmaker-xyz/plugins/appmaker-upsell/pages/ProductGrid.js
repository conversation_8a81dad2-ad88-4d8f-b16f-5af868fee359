import React from 'react';
import { Dimensions } from 'react-native';
import { applyFilters } from '@appmaker-xyz/core';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;

export function ShopifyProductList({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) {
  attributes.specialPrice = applyFilters('product-list-special-price', false);
  // let = false;
  const defaultDataSource = {
    source: 'shopify',
    attributes: {
      pageStateName: 'productList',
      mapping: {
        items: 'data.data.products.edges',
      },
      methodName: 'products',
      params: '{{currentAction.params}}',
      allowFilters: true,
      filterVars: {
        pageState: ['filter', 'sort'],
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  const finalAttributes = {
    cartActionParams: {
      fromCart: true,
    },
    loadingLayout: 'product-grid',
    wishList: true,
    savedItemIds: '{{(pageState.metaData.savedItem.data)}}',
    id: '{{blockItem.node.id}}',
    hasPages: true,
    appmakerAction: {
      pageId: 'productDetail',
      params: {
        pageData: '{{blockItem}}',
      },
      action: 'OPEN_PRODUCT_DETAIL',
    },
    // cartQuantity: '{{getCartQty(appStorageState.checkout,blockItem.node.id)}}',
    title: '{{blockItem.node.title }}',
    isInsideCartPage: true,
    numColumnsPageStateKey: 'numColumns',
    numColumnsDefault: deviceRatio > 1.6 ? '2' : '3',
    gridViewListing: true,
    uri: '{{blockItem.node.images.edges[0].node.url}}',
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    in_stock:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    blockItem: '{{blockItem}}',
    availableForSale:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    regularPrice:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %> <% if( regularPrice>salePrice) { %><%= currencyHelper(regularPrice,blockItem.node.compareAtPriceRange.minVariantPrice.currencyCode) %><% } %>',
    salePrice:
      '{{ currencyHelper(blockItem.node.priceRange.minVariantPrice.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
    onSale:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><%=echo(regularPrice>salePrice)%>',
    salePercentage:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><% if( regularPrice>salePrice) { %><%= parseInt(100-((salePrice/regularPrice)*100 ))%> <%="%"%>%><% } %>',
    thumbnail_meta: {
      height: '{{blockItem.node.images.edges[0].node.height}}',
      width: '{{blockItem.node.images.edges[0].node.width}}',
    },
    bestSeller: '{{blockItem.node.bestseller_tag}}',
    brandName:
      '<% if(checkIfTrueFalse(plugins.shopify.settings.show_vendor_pdp) == true){ %><%=blockItem.node.vendor%><% } %>',
    new_product: '{{blockItem.node.new_tag}}',
    brandColor: { primary: '#B6DCC9', secondary: '#E63F12' },
    saleBadgeStyle: {
      textStyle: {
        color: 'white',
      },
      containerStyle: {
        backgroundColor: '#E63F12',
      },
    },
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    show_last_few_remaining:
      '{{ blockItem.node.totalInventory < 5 && blockItem.node.totalInventory > 0 }}',
    last_few_remaining_text: 'Last Few Remaining',
    ...(attributes && attributes),
    dataSource: attributes.customDataSource
      ? attributes.customDataSource
      : defaultDataSource,
  };
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'appmaker/product-grid-item',
        innerBlocks,
        clientId: 'product-list',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
}
