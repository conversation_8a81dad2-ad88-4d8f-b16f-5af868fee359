import { Image, Pressable, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import Icon from '@appmaker-xyz/uikit/Icons/FontAwesome';
import { AppmakerText, Button, AppTouchable } from '@appmaker-xyz/uikit';
import BlockCard from '@appmaker-xyz/uikit/src/components/organisms/card/BlockCard';

export default function UpsellProductList({
  clientId,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  ...props
}) {
  const pId = blockData?.lineItems?.edges[0]?.node?.variant?.product?.id;
  // const productHandles
  function formatTag(tag) {
    let newTag = tag.toLowerCase().replace(' ', '-');
    return `tag:${newTag}`;
  }
  let tagArray = [
    // 'cross_sell--wow-life-science-multivitamin-for-women-30-numbers',
  ];
  blockData?.lineItems?.edges?.forEach(({ node }) => {
    // console.log();
    const upsellTags = node.variant.product.tags.filter((tag) =>
      tag.match('cross_sell--'),
    );
    // console.log(upsellTags);
    // return upsellTags;
    tagArray = [...tagArray, ...upsellTags];
  }) || [];
  // unique tags
  tagArray = [...new Set(tagArray)];

  // tagArray
  // let tagArray = tagArrays.map((key) => tagArrays[key].label);
  // console.log(tagArray);
  // return null;
  let finalTags = tagArray.map(formatTag).join(' OR ');
  const pagesData = {
    blocks: [
      {
        dependencies: {
          pageState: ['metaData'],
        },
        attributes: {
          dataSource: {
            source: 'shopify',
            attributes: {
              mapping: {
                items: 'data.data.products.edges',
              },
              methodName: 'products',
              params: {
                search: finalTags,
              },
            },
            repeatable: 'Yes',
            repeatItem: 'DataSource',
          },
          appmakerAction: {
            productId: '{{blockItem.node.id}}',
            action: 'OPEN_PRODUCT',
          },
          cartAction: {
            params: {
              product: '{{blockItem.node}}',
              fromCart: true,
            },
            product: '{{blockItem.node}}',
            action: 'ADD_TO_CART',
          },
          man: '{{blockData}}',
          title: '{{blockItem.node.title }}',
          uri: '{{blockItem.node.images.edges[0].node.url}}',
          productType:
            '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
          regularPrice:
            '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %> <% if( regularPrice>salePrice) { %><%= currencyHelper(regularPrice,blockItem.node.compareAtPriceRange.minVariantPrice.currencyCode, blockItem.node.title) %><% } %>',
          salePrice:
            '{{ currencyHelper(blockItem.node.priceRange.minVariantPrice.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
          onSale:
            '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><%=echo(regularPrice>salePrice)%>',
          salePercentage:
            '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><% if( regularPrice>salePrice) { %><%= parseInt(100-((salePrice/regularPrice)*100 ))%> <%="%"%>%><% } %>',

          hasPages: false,
          horizontal: false,
          gridViewListing: false,
        },
        name: 'appmaker/upsell-product-item',
        clientId: 'upsell-cart-recomendations',
        innerBlocks: [],
      },
    ],
  };
  return (
    <BlockCard
      attributes={{
        ...attributes,
        __display: true,
        title: 'Frequently bought together',
        accessButton: '',
        childContainerStyle: { paddingHorizontal: 12, paddingBottom: 8 },
        wholeContainerStyle: { backgroundColor: '#D8DEFF' },
      }}>
      <BlocksView inAppPage={pagesData} {...props} blockData={blockData} />

      {/* <CardItem clientId={clientId} />
        <CardItem clientId={clientId} /> */}
    </BlockCard>
  );
}
