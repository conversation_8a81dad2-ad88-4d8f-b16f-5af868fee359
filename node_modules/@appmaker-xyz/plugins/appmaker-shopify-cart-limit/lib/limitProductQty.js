export function limitProductQty(settings) {
  return async (input, dependencies) => {
    input = await Promise.resolve(input);
    const limit =
      settings?.enable_item_quantity_limit === true &&
      settings?.item_quantity_value_limit &&
      typeof settings?.item_quantity_value_limit === 'string'
        ? parseInt(settings?.item_quantity_value_limit, 10)
        : 99999;
    const qtyLimitErrorMsg =
      settings?.item_quantity_limit_error_message ||
      'You cannot add more of this product to your cart';

    const filteredLineItemToAdd = [];
    const filteredLineItemToUpdate = [];
    const messages = [];
    const cartProducts = input?.currentCart?.lineItems?.edges;
    const toAddProducts =
      input?.lineItemsToAdd?.length > 0 ? input?.lineItemsToAdd : [];
    const toUpdateProduct =
      input?.lineItemsToUpdate?.length > 0 ? input?.lineItemsToUpdate : [];

    if (toAddProducts?.length > 0) {
      toAddProducts.forEach((lineItem) => {
        const filteredLineItem = lineItem;
        const linItemVarinatId =
          lineItem?.variantId || lineItem?.variant?.node?.id;
        const cartProduct = cartProducts?.find(
          (x) => x?.node?.variant?.id === linItemVarinatId,
        );
        const cartQty = cartProduct?.node?.quantity || 0;
        const toAddQty = lineItem?.quantity || 0;
        let limitReached = cartQty >= limit;

        // if the product is in the cart and the quantity to add is more than the limit
        if (cartQty + toAddQty > limit) {
          limitReached = true;
          // set the quantity to the limit
          if (limit - cartQty > 0) {
            filteredLineItem.quantity = limit - cartQty;
          } else {
            return;
          }
        }

        // if the product is not in the cart and the quantity to add is more than the limit
        if (!cartProduct && toAddQty > limit) {
          messages.push({
            message: qtyLimitErrorMsg,
          });
          filteredLineItem.quantity = limit;
        }

        if (limitReached) {
          messages.push({
            message: qtyLimitErrorMsg,
          });
        }

        filteredLineItemToAdd.push(filteredLineItem);
      });
    }

    if (toUpdateProduct?.length > 0) {
      toUpdateProduct.forEach((lineItem) => {
        const filteredLineItem = lineItem;
        if (lineItem?.quantity > limit) {
          messages.push({
            message: qtyLimitErrorMsg,
          });
          filteredLineItem.quantity = limit; // set the quantity to the limit
        }
        filteredLineItemToUpdate.push(filteredLineItem);
      });
    }

    input.lineItemsToAdd = filteredLineItemToAdd;
    input.lineItemsToUpdate = filteredLineItemToUpdate;
    input.messages = messages;

    return input;
  };
}
