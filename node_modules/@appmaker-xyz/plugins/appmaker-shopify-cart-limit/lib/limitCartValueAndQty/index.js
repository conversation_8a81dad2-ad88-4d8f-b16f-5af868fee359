import { parse } from '@appmaker-xyz/core';
import { isEmpty } from 'lodash';

function calculateTotalCartValue(
  checkout,
  discountTypesToInclude = [
    {
      label: 'ALL',
      value: 'ALL',
    },
  ],
  include_free_gift_in_total_value,
) {
  // sum = parseFloat(currentCart?.totalPrice.amount || 0);
  let sum = 0;
  sum += checkout.lineItems.edges.reduce((acc, item) => {
    const currentLineItem = item.node;
    let currentLineItemPrice = 0;
    const quantity = currentLineItem?.quantity;
    const currentLineItemAmount = parseFloat(
      currentLineItem?.variant?.price?.amount,
    );
    currentLineItemPrice = quantity * currentLineItemAmount;
    const discountAllocations = currentLineItem?.discountAllocations;

    const isFreeGit = currentLineItem?.customAttributes?.find(
      (x) => x?.key === 'appmaker_free_gift',
    );

    if (discountAllocations && discountAllocations.length > 0) {
      discountAllocations.forEach((discount) => {
        // check if the discount type is included in the list of discount types to include
        if (
          !(
            isFreeGit &&
            include_free_gift_in_total_value === false &&
            discount?.discountApplication?.__typename ===
              'ScriptDiscountApplication'
          ) &&
          discountTypesToInclude.length > 0 &&
          !discountTypesToInclude.find((type) => {
            return (
              type.value === 'ALL' ||
              type.value === discount?.discountApplication?.__typename
            );
          })
        ) {
          return;
        }
        currentLineItemPrice -= parseFloat(discount?.allocatedAmount?.amount);
      });
    }
    return acc + currentLineItemPrice;
  }, sum);
  return sum;
}

export async function limitCartValueAndQty({ settings, input, dependencies }) {
  const { appStorageState, handleAction } = dependencies;
  const { checkout } = appStorageState;

  const {
    total_value_limit,
    quantity_value_limit,
    enable_quantity_limit = false,
    total_value_limit_error = false,
    quantity_limit_error_message = false,
    enable_limit_cart_total_value = true,
    include_free_gift_in_total_value = false,
    enable_item_quantity_limit = false,
    item_quantity_value_limit = 99999,
    item_quantity_limit_error_message = false,
    include_discount_code_types = [
      {
        label: 'ALL',
        value: 'ALL',
      },
    ],
    disable_checkout = false,
  } = settings;

  if (enable_limit_cart_total_value) {
    let totalCartValue = calculateTotalCartValue(
      checkout,
      include_discount_code_types,
      include_free_gift_in_total_value,
    );
    const totalValueLimit = parseFloat(total_value_limit);
    let totalDifference = totalCartValue - totalValueLimit;
    totalDifference = totalDifference.toFixed(2);
    const totalValueLimitCartMessage = !isEmpty(total_value_limit_error)
      ? parse({
          template: {
            message: total_value_limit_error,
          },
          data: {
            checkout,
            totalValueLimit,
            totalDifference,
          },
        })?.message
      : `Please remove ${totalDifference} worth of items from your cart to proceed with checkout`;

    if (totalCartValue > totalValueLimit) {
      console.log('limitCartValueAndQty value limit error');
      if (disable_checkout) {
        input.canCheckout = false;
      }
      if (handleAction) {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: totalValueLimitCartMessage },
        });
      }
      return input;
    }
  }
  if (enable_quantity_limit) {
    const quantityLimit = parseFloat(quantity_value_limit);
    const cartItemsCount = checkout.lineItems.edges
      .map((item) => item.node.quantity)
      .reduce((a, b) => a + b, 0);

    let totalDifference = cartItemsCount - quantityLimit;

    const quantityLimitCartMessage = !isEmpty(quantity_limit_error_message)
      ? parse({
          template: {
            message: quantity_limit_error_message,
          },
          data: {
            checkout,
            totalDifference,
          },
        })?.message
      : `You have reached the maximum quantity allowed (${quantityLimit}). Please remove items to continue your checkout.`;

    if (cartItemsCount > quantityLimit) {
      console.log('limitCartValueAndQty quantity limit error');
      if (handleAction) {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: quantityLimitCartMessage },
        });
      }
      if (disable_checkout) {
        input.canCheckout = false;
      }
      return input;
    }
  }
  if (enable_item_quantity_limit && disable_checkout) {
    // check if any item has quantity more than the limit
    const itemWithQuantityMoreThanLimit = checkout.lineItems.edges.find(
      (item) => item.node.quantity > item_quantity_value_limit,
    );

    if (itemWithQuantityMoreThanLimit) {
      input.canCheckout = false;
      if (handleAction) {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: {
            title:
              item_quantity_limit_error_message ||
              `You have reached the maximum quantity allowed (${item_quantity_value_limit}) for "${itemWithQuantityMoreThanLimit.node.title}". Please remove items to continue your checkout.`,
          },
        });
      }
      return input;
    }
  }
  return input;
}
