import React, { useState, useEffect } from 'react';
import { Button } from '@appmaker-xyz/uikit';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit';

const ResendOTP = ({ attributes, onAction, coreDispatch }, props) => {
  const { timerLimit } = attributes;
  const [time, setTime] = useState(timerLimit);

  const [disableButton, setDisableButton] = useState(true);
  // const [loading, setLoading] = useState(false);
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  useEffect(() => {
    let interval;
    if (disableButton === true) {
      interval = setInterval(() => {
        setTime((t) => {
          if (t - 1 === 0) {
            clearInterval(interval);
            setDisableButton(false);
            return timerLimit;
          }
          return t - 1;
        });
      }, 1000);
    }
    return () => interval && clearInterval(interval);
  }, [disableButton]);
  const onPress = async () => {
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: true,
    });
    onAction && (await onAction(attributes.appmakerAction));
    coreDispatch({
      type: 'SET_VALUE',
      name: 'loadingButton',
      value: false,
    });
    setDisableButton(true);
  };
  return (
    <Button
      disabled={disableButton}
      {...attributes}
      small={true}
      wholeContainerStyle={styles.recentOtpButton}
      onPress={onPress}>{`Resend OTP ${
      time !== timerLimit ? `in ${time}s` : ''
    }`}</Button>
  );
};
const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    recentOtpButton: {
      backgroundColor: 'transparent',
      marginTop: spacing.base,
    },
  });

export default ResendOTP;
