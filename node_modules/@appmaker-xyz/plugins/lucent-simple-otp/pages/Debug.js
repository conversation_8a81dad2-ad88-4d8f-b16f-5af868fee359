// import { color, spacing } from '../../styles';
import { colors as configColors } from '@appmaker-xyz/app-config/newConfig';
import { color, spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { appSettings } from '@appmaker-xyz/core';

const CustomSignUpDebug = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-register',
          attributes: {
            appmakerAction: {
              action: 'LUCENT_DEBUG_SIGNUP',
            },
            content: 'Create Account',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),
            __appmakerStylesClassName: 'registerButton',
          },
          contextValues: true,
        },
      ],
    },
  ],
};
export default CustomSignUpDebug;
