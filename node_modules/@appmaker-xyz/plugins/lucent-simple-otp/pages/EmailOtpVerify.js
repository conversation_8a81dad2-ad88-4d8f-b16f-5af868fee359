import { spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { appSettings } from '@appmaker-xyz/core';

const blocks = [
  {
    name: 'appmaker/text',
    attributes: {
      content: 'Verify Email',
      category: 'h1Heading',
      style: {
        marginBottom: spacing.lg,
      },
    },
  },
  {
    name: 'appmaker/floating-label-input',
    attributes: {
      label: 'Enter OTP',
      name: 'otp',
      status: 'grey',
      caption: 'Enter OTP sent to your email',
      type: 'number',
      autoAction: {
        triggerAt:
          "{{plugins['lucent-simple-otp'].settings.total_otp_digit_count}}",
        appmakerAction: {
          action: 'LUCENT_VERIFY_EMAIL_OTP',
        },
      },
      maxLength:
        "{{plugins['lucent-simple-otp'].settings.total_otp_digit_count}}",
      defaultValue: '',
    },
  },
  {
    clientId: 'emamil-otp-sumbit',
    name: 'appmaker/button',
    attributes: {
      content: 'Verify OTP',
      loading: false,
      baseSize: true,
      appmakerAction: {
        action: 'LUCENT_VERIFY_EMAIL_OTP',
      },
      wholeContainerStyle: {
        borderRadius: 0,
        backgroundColor: appSettings.getOption('primary_button_color'),
      },
      fontColor: appSettings.getOption('primary_button_text_color'),
      __appmakerStylesClassName: 'registerButton',
    },
  },
  {
    clientId: 'otp-resend',
    name: 'appmaker/otp-resend-button',
    attributes: {
      content: 'Resend OTP',
      timerLimit: 60,
      loading: false,
      small: true,
      appmakerAction: {
        action: 'LUCENT_REQUEST_EMAIL_OTP',
      },
      wholeContainerStyle: {
        marginTop: spacing.lg,
        borderRadius: 0,
        backgroundColor: 'transparent',
      },
      fontColor: appSettings.getOption('primary_button_color'),
    },
  },
];
const EmailOtpRequest = {
  type: 'normal',
  title: '',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#ffffff',
    },
    contentContainerStyle: {
      flex: 1,
      justifyContent: 'center',
      paddingHorizontal: spacing.base,
      paddingTop: spacing.xl * 2,
    },
  },
  blocks: blocks,
  // fixedFooter:
};
export default EmailOtpRequest;
