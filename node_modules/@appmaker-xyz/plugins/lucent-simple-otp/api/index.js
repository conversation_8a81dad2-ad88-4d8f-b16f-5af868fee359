import axios from 'axios';
let shop_name = '';
export function setShop(shop) {
  shop_name = shop;
}
// const url = 'https://vrmjasiwra.execute-api.ap-south-1.amazonaws.com';
const url = 'https://omqkhavcch.execute-api.ap-south-1.amazonaws.com';
export async function requestOtp({ username, action, type }) {
  var data = { username: username, type: type || 'email' };
  var config = {
    method: 'post',
    url: `${url}/simplyotplogin/v5/otp`,
    headers: {
      shop_name,
      action: action ? action : 'sendOTP',
      'Content-Type': 'application/json',
    },
    data: data,
  };
  const response = await axios(config);
  return response.data;
}

export async function resendOtp({ username, action, type }) {
  const data = { username: username, type: type || 'phone' };
  var config = {
    method: 'post',
    url: `${url}/simplyotplogin/v5/otp`,
    headers: {
      shop_name,
      action: action ? action : 'resendOTP',
      'Content-Type': 'application/json',
    },
    data: data,
  };
  const response = await axios(config);
  return response.data;
}

export async function verifyOtp({ username, type, otp, otp_id }) {
  const data = {
    username,
    otp,
    type,
    otp_id,
    checkout_url: '',
    domain: shop_name,
  };

  var config = {
    method: 'post',
    url: `${url}/simplyotplogin/v5/otp`,
    headers: {
      shop_name,
      action: 'verifyOTP',
      'Content-Type': 'application/json',
    },
    data: data,
  };
  const response = await axios(config);
  return response.data;
}

export async function updateCustomer({
  first_name,
  last_name,
  username,
  email,
  phone_no,
  type,
  otp,
  otp_id,
  accept_email_marketing,
  accept_sms_marketing,
  accept_whatsapp_marketing,
}) {
  const data = {
    first_name,
    last_name,
    phone_no,
    email,
    username,
    otp,
    type,
    otp_id,
    checkout_url: '',
    domain: shop_name,
    accept_email_marketing,
    accept_sms_marketing,
    accept_whatsapp_marketing,
  };

  var config = {
    method: 'post',
    url: `${url}/simplyotplogin/v5/otp`,
    headers: {
      shop_name,
      action: 'updateEmail',
      'Content-Type': 'application/json',
    },
    data: data,
  };
  const response = await axios(config);
  return response.data;
}
