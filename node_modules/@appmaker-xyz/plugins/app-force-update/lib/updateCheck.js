/* eslint-disable radix */
export function shouldForceUpdate({
  serverConfig,
  platform,
  current_version_code,
}) {
  try {
    const {
      force_update_android,
      force_update_ios,
      android_minimum_app_version_required,
      ios_minimum_app_version_required,
    } = serverConfig;
    if (platform === 'android' && force_update_android === true) {
      return (
        parseInt(current_version_code) <
        parseInt(android_minimum_app_version_required)
      );
    }
    if (platform === 'ios' && force_update_ios === true) {
      return (
        parseInt(current_version_code) <
        parseInt(ios_minimum_app_version_required)
      );
    }
  } catch (error) {
    console.log(error);
  }
  return false;
}
