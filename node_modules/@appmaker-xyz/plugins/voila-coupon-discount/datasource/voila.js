import axios from 'axios';
import { usePluginStore } from '@appmaker-xyz/core';

export default class SearchTapDataSource {
  constructor(config) {
    this.config = config;
  }
  async getCouponList(params) {
    // alert(params);
    const shop_domain = params;
    const { data } = await axios.get(
      `https://voilaapps.co/ajax/get_customizer_api.php?shop_domain=${shop_domain}`,
    );
    return data;
  }
}
