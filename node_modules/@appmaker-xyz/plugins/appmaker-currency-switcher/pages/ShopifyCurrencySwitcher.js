const page = {
  title: 'Change Currency',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: 6,
      paddingTop: 8,
    },
  },
  blocks: [
    {
      clientId: 'curency-switcher-items',
      name: 'appmaker/currency-chooser',
      attributes: {
        containerStyle: {
          flex: 1,
        },
        appmakerAction: {
          params: { currency: 'INR' },
          action: 'SET_CURRENCY',
        },
        items:
          "{{plugins['appmaker-currency-switcher'].settings.currency.currency_list}}",
        selectedItem:
          "<%=plugins['appmaker-currency-switcher'].settings.currency.default_currency_code%>",
      },
    },
  ],
};
export default page;
