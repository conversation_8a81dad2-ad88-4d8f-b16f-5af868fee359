import { usePageState } from '@appmaker-xyz/core';
import { usePluginStore } from '@appmaker-xyz/core';
import { isEmpty } from 'lodash';
import { useReducer } from 'react';
import { useEffect } from 'react';
import { getFreeGiftForCart } from '../freeGiftHelper';
const initialState = {
  status: 'idle',
  message: 'Checking for free gift...',
  hasGifts: false,
  cartAmountRequriedForFreeGift: null,
};
function reducer(state, action) {
  const { type, payload } = action;
  switch (type) {
    case 'start':
      return {
        ...state,
        status: 'loading',
        ...payload,
      };
    case 'success':
      return {
        ...state,
        status: 'success',
        ...payload,
      };
    case 'error':
      return {
        ...state,
        status: 'error',
        ...payload,
      };
    default:
      throw new Error();
  }
}
export function useFreeGift({ onAction }) {
  const [state, dispatch] = useReducer(reducer, initialState);
  // const [currentTry, setTry] = useState(0);
  const cartData = usePageState((_state) => _state.blockData);
  const setPageStateVar = usePageState((_state) => _state.setPageStateVar);
  const pageStateMaxTry = usePageState((_state) => _state.pageStateMaxTry || 0);
  const pluginSettings = usePluginStore(
    (_state) => _state.plugins['appmaker-free-gift'],
  );
  const maxTryLimit = pluginSettings?.settings?.maxTryLimit || 3;
  const showBanner = pluginSettings?.settings?.show_free_gift_banner;
  useEffect(() => {
    async function sync() {
      if (isEmpty(cartData)) {
        return;
      }
      try {
        const freeGiftProducts = getFreeGiftForCart({
          cart: cartData,
          free_gifts: pluginSettings.settings.free_gifts,
        });
        dispatch({
          type: 'start',
          payload: {
            type: 'start-to-check-for-free-gift',
          },
        });
        const {
          freeGiftsToAdd,
          freeGiftListAlreadyInCart,
          cartAmountRequriedForFreeGift,
        } = freeGiftProducts;

        if (freeGiftsToAdd.length > 0) {
          setPageStateVar('pageStateMaxTry', pageStateMaxTry + 1);
          dispatch({
            type: 'start',
            payload: {
              message: 'Adding your free gift',
              type: 'free-gift-adding',
            },
          });
          await onAction({
            action: 'ADD_FREE_GIFTS',
            freeGiftsToAdd,
          });
          dispatch({
            type: 'success',
            payload: {
              message: 'Free gift added to cart',
              type: 'free-gift-added',
            },
          });
        } else if (freeGiftListAlreadyInCart.length > 0) {
          dispatch({
            type: 'success',
            payload: {
              message: 'You have a free gift in cart',
              type: 'free-gift-already-in-cart',
            },
          });
        } else if (cartAmountRequriedForFreeGift) {
          dispatch({
            type: 'success',
            payload: {
              type: 'free-gift-amount-required',
              message: `Free gift available on ${cartAmountRequriedForFreeGift} above`,
              cartAmountRequriedForFreeGift,
            },
          });
        } else {
          dispatch({
            type: 'success',
            payload: {
              type: 'free-gift-not-active',
            },
          });
        }
      } catch (error) {
        console.log(error, 'freegift-error');
      }
    }
    if (pageStateMaxTry < maxTryLimit) {
      console.log('cart-sync', pageStateMaxTry);
      sync();
    }
  }, [cartData, pluginSettings]);
  return [
    state,
    dispatch,
    {
      showBanner,
    },
  ];
}
