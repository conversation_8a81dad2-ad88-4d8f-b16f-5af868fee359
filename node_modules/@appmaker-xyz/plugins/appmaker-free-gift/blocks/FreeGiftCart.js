/* eslint-disable react-native/no-inline-styles */
import { View, ActivityIndicator } from 'react-native';
import React from 'react';
import { useFreeGift } from '../hooks/useFreeGift';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { AppmakerText } from '@appmaker-xyz/uikit';
const icons = {
  'free-gift-amount-required': 'gift',
  'free-gift-already-in-cart': 'gift',
  'free-gift-added': 'check',
};
export default function FreeGiftCart({ attributes, onAction }) {
  const [state, , options] = useFreeGift({ onAction });

  // const title = 'Free Gift Available';
  // const color = '#047857';
  // const iconName = 'gift';

  const title = 'Congratulations!';
  const color = '#0F766E';
  const iconName = icons[state.type] || 'gift';

  // const title = 'Free Gift Available';
  // const color = '#15803D';
  // const iconName = 'gift';

  // const title = 'Adding your free gift ...';
  const loading = state.status === 'loading';
  // const color = '#047857';

  return state.type === 'free-gift-not-active' ||
    state.type ===
      'start-to-check-for-free-gift' ? null : !options?.showBanner ? null : (
    <View
      style={{
        backgroundColor: `${color}1A`,
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
      }}>
      {!loading ? (
        <Icon
          name={iconName}
          size={24}
          color={color}
          style={{
            marginRight: 8,
            padding: 12,
            backgroundColor: `${color}1A`,
            borderRadius: 32,
          }}
        />
      ) : (
        <ActivityIndicator
          color={color}
          style={{
            marginRight: 8,
            padding: 12,
            backgroundColor: `${color}1A`,
            borderRadius: 32,
          }}
        />
      )}

      <View>
        <AppmakerText fontColor={color} category="bodyParagraphRegular">
          {state.message}
        </AppmakerText>
      </View>
    </View>
  );
}
