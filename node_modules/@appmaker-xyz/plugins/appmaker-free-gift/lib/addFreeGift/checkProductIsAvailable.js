import { shopifyIdHelper } from '@appmaker-xyz/shopify';
import { appmaker } from '@appmaker-xyz/core';

export default async function checkProductIsAvailable({
  input,
  productData,
  variantId,
  dependencies,
}) {
  let isProductAvailable = false;
  const { log } = dependencies;

  if (productData?.availableForSale) {
    if (productData?.variants?.edges?.length > 0) {
      variantId = shopifyIdHelper(variantId);
      const freeVariant = productData.variants.edges.find(
        (variant) => variant.node.id === variantId,
      );
      isProductAvailable = freeVariant?.node?.availableForSale
        ? variantId
        : false;
      log('variant availableForSale => ', variantId, isProductAvailable);
    }
  }

  log('isProductAvailable', variantId, isProductAvailable);

  isProductAvailable = await appmaker.applyFilters(
    'appmaker-free-gift-is-varaint-available',
    isProductAvailable,
    productData,
    variantId,
  );

  log('isProductAvailable after filter', variantId, isProductAvailable);

  return isProductAvailable;
}
