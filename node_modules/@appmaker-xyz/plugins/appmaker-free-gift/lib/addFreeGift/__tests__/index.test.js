// import { getAllFreeGifts } from '../index';
// import { freeGiftSettings, cartData } from '../../../sampleData';

// jest.mock('@appmaker-xyz/core', () => ({
//   parse: jest.fn(),
// }));

// describe('getAllFreeGifts', () => {
//   it('should return an object with free gifts to add and existing free gifts', async () => {
//     const freeGifts = await getAllFreeGifts({
//       input: {
//         currentCart: cartData,
//         lineItemsToAdd: [],
//         lineItemsToRemove: [],
//         lineItemsToUpdate: [],
//       },
//       freeGiftConfig: freeGiftSettings,
//       freeGiftDependencies: {},
//     });

//     expect(freeGifts).toHaveProperty('freeGiftsToAdd');
//     expect(freeGifts).toHaveProperty('existingFreeGifts');
//   });

//   it('should return an object with next free gifts if no free gifts are available', async () => {
//     const freeGifts = await getAllFreeGifts({
//       input: {
//         currentCart: cartData,
//         lineItemsToAdd: [],
//         lineItemsToRemove: [],
//         lineItemsToUpdate: [],
//       },
//       freeGiftConfig: freeGiftSettings,
//       freeGiftDependencies: {},
//     });

//     expect(freeGifts).toHaveProperty('nextFreeGifts');
//   });
// });
