import { getFreeGiftForCart, hasEligibility } from '../freeGiftHelper';
import {
  cartData,
  cartDataWithFreeGiftInCart,
  freeGiftSettings,
} from '../sampleData';
// cartData

// test('should first', () => {
describe('Free Gift eligibility', () => {
  describe('Cart Value', () => {
    it('free gift above 200', () => {
      const free_gift_item = freeGiftSettings.free_gifts[0];
      const freeGiftResponse = hasEligibility(cartData, free_gift_item);
      expect(freeGiftResponse.eligibility).toBe(true);
      expect(freeGiftResponse.products).toBe(free_gift_item.products);
    });
    it('no free below 1500', () => {
      const free_gift_item = freeGiftSettings.free_gifts[1];
      const freeGiftResponse = hasEligibility(cartData, free_gift_item);
      expect(freeGiftResponse.eligibility).toBe(false);
    });
    it('gift is disabled', () => {
      const free_gift_item = freeGiftSettings.free_gifts[0];
      const freeGiftResponse = hasEligibility(cartData, {
        ...free_gift_item,
        status: false,
      });
      expect(freeGiftResponse.eligibility).toBe(false);
    });
  });
});
describe('Free Gift all cart eligibility', () => {
  describe('Cart Value', () => {
    it('Cart not eligible for gift', () => {
      const [, above1500] = freeGiftSettings.free_gifts;
      const free_gifts = [above1500];
      const freeGiftResponse = getFreeGiftForCart({
        cart: cartData,
        free_gifts,
      });
      expect(freeGiftResponse.freeGiftsToAdd.length).toBe(0);
      expect(freeGiftResponse.freeGiftListAlreadyInCart.length).toBe(0);
      expect(freeGiftResponse.cartAmountRequriedForFreeGift).toBe(1500);
    });
    it('Cart is empty and have free gift', () => {
      const [, above1500] = freeGiftSettings.free_gifts;
      const free_gifts = [above1500];
      const freeGiftResponse = getFreeGiftForCart({
        cart: {},
        free_gifts,
      });
      expect(freeGiftResponse.freeGiftsToAdd.length).toBe(0);
      expect(freeGiftResponse.freeGiftListAlreadyInCart.length).toBe(0);
      expect(freeGiftResponse.cartAmountRequriedForFreeGift).toBe(1500);
    });
    it('No free gift in cart', () => {
      const [above200] = freeGiftSettings.free_gifts;
      const free_gifts = [above200];
      const freeGiftResponse = getFreeGiftForCart({
        cart: cartData,
        free_gifts,
      });
      expect(freeGiftResponse.freeGiftsToAdd.length).toBe(1);
      expect(freeGiftResponse.freeGiftsToAdd[0].variant_gid).toBe(
        above200.products[0].variant_gid,
      );
      expect(freeGiftResponse.freeGiftListAlreadyInCart.length).toBe(0);
    });
    it('free gift already in cart', () => {
      const [above200] = freeGiftSettings.free_gifts;
      const free_gifts = [above200];
      const freeGiftResponse = getFreeGiftForCart({
        cart: cartDataWithFreeGiftInCart,
        free_gifts,
      });
      expect(freeGiftResponse.freeGiftsToAdd.length).toBe(0);
      expect(freeGiftResponse.freeGiftListAlreadyInCart.length).toBe(1);
      expect(freeGiftResponse.freeGiftListAlreadyInCart).toEqual(
        above200.products,
      );
    });
  });
});
