let pageData = {
  name: 'appmaker/blocksView',
  clientId: 'form-field-qwickcilver',
  attributes: {
    __display:
      '{{blockItem.node.productType === `qwikcilver_gift_card` ? true : false }}',
    headerShown: false,
    rootContainerStyle: {
      paddingTop: 12,
      paddingHorizontal: 12,
      backgroundColor: '#fff',
    },
  },
  innerBlocks: [
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: `Recipient's Name*`,
        name: 'properties[_recipient_name]',
        status: 'grey',
        nextFieldName: 'address1',
      },
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: `Recipient's Email*`,
        name: 'properties[_Gift to Email]',
        status: 'grey',
        nextFieldName: 'address1',
      },
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: `Sender's Name*`,
        name: 'properties[_sender_name]',
        status: 'grey',
        nextFieldName: 'address1',
      },
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: `Sender's Email*`,
        name: 'properties[_sender_email]',
        status: 'grey',
        nextFieldName: 'address1',
      },
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: `Message ( Optional) `,
        name: 'properties[_QC Message]',
        status: 'grey',
        nextFieldName: 'address1',
      },
    },
  ],
};

export default pageData;
