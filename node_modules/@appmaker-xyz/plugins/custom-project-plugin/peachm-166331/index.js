import { appmaker } from '@appmaker-xyz/core';

import { CUSTOM_REQUEST_OTP, CUSTOM_VERIFY_OTP } from './actions';
import { enableCustomEvents } from './custom-events';
import { addWidgetinProductDetailPage } from './helper';

const Plugin = {
  id: 'peachm-166331',
  name: 'Peachm-166331',
  activate,
};

export function activate({ settings }) {
  // console.log('peachm-166331', settings);
  // const add
  enableCustomEvents();
  if (settings?.enableWidget === true) {
    addWidgetinProductDetailPage();
  }
  appmaker.actions.registerAction('CUSTOM_REQUEST_OTP', CUSTOM_REQUEST_OTP);
  appmaker.actions.registerAction('CUSTOM_VERIFY_OTP', CUSTOM_VERIFY_OTP);
}
export default Plugin;
