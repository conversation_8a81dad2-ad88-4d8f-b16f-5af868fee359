import { requestOtp, verifyOtp } from './api/otp';

export async function CUSTOM_REQUEST_OTP({ params }, deps) {
  let { phone } = params;
  // phone = '8089906560';
  const data = await requestOtp({
    phone,
  });
  const { handleAction } = deps;

  if (data?.error === 'No customer found.') {
    handleAction({
      action: 'OPEN_REGISTER',
    });
    throw new Error(data?.error);
  }
}

export async function CUSTOM_VERIFY_OTP({ params }, deps) {
  const { handleAction } = deps;
  let { phone, otp } = params;
  const data = await verifyOtp({
    phone,
    otp,
  });
  if (data.error) {
    throw new Error(data?.error);
  }
  const multipassToken = data.token;
  await handleAction(
    {
      action: 'LOGIN_USER_MULTIPASS',
      params: {
        multipassToken,
      },
    },
    deps,
  );
}
