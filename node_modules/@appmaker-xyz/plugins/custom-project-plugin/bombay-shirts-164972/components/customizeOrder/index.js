import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';

import { Layout } from '@appmaker-xyz/uikit';

// import { AppmakerText, Button } from '../../index';
import Icon from 'react-native-vector-icons/Feather';
import { shopifyIdHelper } from '@appmaker-xyz/shopify';

import { usePageState } from '@appmaker-xyz/core';
import { useApThemeState } from '@appmaker-xyz/uikit/src/index';
import { AppmakerText, Button } from '@appmaker-xyz/uikit/src/components/index';
import { usePluginStore } from '@appmaker-xyz/core';
import ModalWebview from '../ModalWebview';
import { getProductProperties } from '../../helper/index';
import { appStorageApi } from '@appmaker-xyz/core';
// function formatDataForProduct(jsonData) {
//   const formated = {};
//   Object.keys(jsonData).forEach((key) => {
//     if (jsonData[key] === '' || !key.match(/properties/)) {
//     } else {
//       const newKey = key.replace('properties[', '').replace(']', '');
//       formated[newKey] = jsonData[key];
//     }
//   });
//   return formated;
// }

function getBookingUrl({ onlineStoreUrl, handle, vendor }) {
  // picarioview#modal appview
  console.log('getBookingUrl', onlineStoreUrl);
  const productUrl =
    onlineStoreUrl ||
    `https://bombayshirt-india.myshopify.com/products/${handle}`;
  const preview_theme_id = onlineStoreUrl ? '130834071715' : '131222175914';
  let accessTokenParams;
  //  ;
  try {
    const access_token = appStorageApi().getState().user?.accessToken;
    // if
    accessTokenParams = `&accessToken=${access_token}`;
  } catch (error) {}
  let finalURL = `${productUrl}/?preview_theme_id=${preview_theme_id}&view=picarioview${accessTokenParams}`;
  if (vendor === 'Korra') {
    finalURL = finalURL + '&view=korra-customise';
  }
  return finalURL;
}
const CustomizeOrder = ({
  attributes,
  onPress,
  onAction,
  blockData,
  pageState,
}) => {
  const [timeSlot, setTimeSlot] = useState('');
  const [visible, setVisibility] = useState(false);
  const open = () => setVisibility(true);
  const close = () => setVisibility(false);
  const { appmakerAction } = attributes;
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing });

  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  const setpageState = usePageState((state) => state.setState);
  const meta_custom_attributes_value =
    attributes?.blockItem?.node?.global_meta_custom_attributes?.value;
  useEffect(() => {
    // delete all the items except the first one in array of all objects inside an array
    if (meta_custom_attributes_value) {
      const array = JSON.parse(
        attributes.blockItem.node.global_meta_custom_attributes.value,
      ).designAttributes;
      let newArray = array.map((item, index) => {
        item.values.splice(1);
        return item;
      });
      if (attributes?.blockItem?.node?.vendor === 'Korra') {
        newArray = newArray.filter(
          (item) => item.attributeName !== 'Fabric Care',
        );
      }
      // json.stringify(newArray) and add slashes for double quotes to the string
      const attributesObject = {
        attributes: newArray,
      };
      const newString = JSON.stringify(attributesObject);
      // const newStringWithSlashes = newString.replace(/"/g, '\\"');
      const finalArray = [
        {
          name: 'properties[_attributes]',
          value: newString,
        },
        {
          name: 'properties[_product_type]',
          value: attributes?.blockItem?.node?.productType,
        },
      ];

      const productAttributes = getProductProperties(finalArray);
      setpageState((state) => {
        if (!state.productAttributes) {
          state.productAttributes = {};
        }
        state.productAttributes = {
          ...state.productAttributes,
          ...productAttributes,
        };
      });
    }
    // if (pageState.productAttributes) {
    //   setTimeSlot(pageState.productAttributes.timeSlot);
    // }
  }, [meta_custom_attributes_value]);

  const onMessage = (event) => {
    const { data } = event.nativeEvent;

    const jsonData = JSON.parse(data);
    // if (jsonData['properties[Date]']) {
    try {
      // const selectedColor = jsonData.payload.selectedColor.name;
      // const selectedSize = jsonData.payload.selectedSize.name;
      // console.log(`${selectedColor} : ${selectedSize}---`);
      // setTimeSlot(`${selectedColor} , ${selectedSize}`);
    } catch (error) {
      console.log('error', error);
    }

    // }
    setVisibility(false);
    const productAttributes = getProductProperties(jsonData);

    setpageState((state) => {
      if (!state.productAttributes) {
        state.productAttributes = {};
      }
      state.productAttributes = {
        ...state.productAttributes,
        ...productAttributes,
      };
    });
  };
  const { productId, variantId } = attributes;
  const shopId = usePluginStore((state) => 'sss');
  let normalProductId = '',
    normalVariantId = '';
  try {
    normalProductId = shopifyIdHelper(productId, true);
    normalVariantId = shopifyIdHelper(variantId, true);
  } catch (error) {
    console.log(error);
  }

  return (
    <Layout style={styles.container}>
      <Layout style={styles.barContainer}>
        <AppmakerText category="bodyParagraphBold" style={styles.title}>
          Customise your order
        </AppmakerText>
        <Layout style={styles.barContainer}>
          {timeSlot ? (
            <AppmakerText category="bodyParagraphBold" style={styles.timeText}>
              {timeSlot}
            </AppmakerText>
          ) : (
            <Button
              onPress={open}
              small
              status="dark"
              outline
              wholeContainerStyle={styles.button}>
              Customise
            </Button>
          )}
          {timeSlot ? (
            <Icon
              name="x"
              size={12}
              onPress={() => setTimeSlot('')}
              style={styles.icon}
            />
          ) : null}
        </Layout>
      </Layout>
      <ModalWebview
        visible={visible}
        close={close}
        uri={getBookingUrl({
          onlineStoreUrl: blockData.node.onlineStoreUrl,
          handle: blockData.node.handle,
          vendor: blockData.node.vendor,
          shopId,
          productId: normalProductId,
          variantId: normalVariantId,
        })}
        onMessage={onMessage}
      />
    </Layout>
  );
};

const allStyles = ({ spacing, color }) =>
  StyleSheet.create({
    container: {
      backgroundColor: color.white,
      padding: spacing.small,
      flex: 1,
      marginBottom: spacing.nano,
    },
    barContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: color.light,
      paddingVertical: spacing.mini,
      paddingStart: spacing.small,
      paddingEnd: spacing.nano,
      borderRadius: spacing.small,
    },
    title: { flex: 1 },
    view: {
      borderRadius: spacing.small,
      overflow: 'hidden',
    },
    icon: {
      marginLeft: spacing.nano,
      padding: spacing.mini,
    },
    timeText: {
      backgroundColor: color.light,
      padding: spacing.nano,
      borderRadius: spacing.nano,
    },
    button: { borderRadius: 100 },
  });

export default CustomizeOrder;
