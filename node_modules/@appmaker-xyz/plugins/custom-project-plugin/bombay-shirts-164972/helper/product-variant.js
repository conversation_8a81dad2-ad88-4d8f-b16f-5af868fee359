import produce from 'immer';

export function expandVariantToProducts(products) {
  const finalProducts = [];
  products.map((product) => {
    const variants = product.node.variants.edges;
    const colorVariants = {};
    variants.map((variant) => {
      const colorName = variant.node.selectedOptions[0].name;
      const colorValue = variant.node.selectedOptions[0].value;
      const objectKey = colorName + '-' + colorValue;
      if (colorVariants[objectKey] === undefined) {
        const newProduct = produce(product, (draft) => {
          // draft.node.title =' saleeh';
          draft.node.images.edges[0].node.src = variant.node.image.src;
          draft.node.images.edges[0].node.url = variant.node.image.url;
          draft.node.defaultSelectedOptions = variant.node.selectedOptions;
        });
        colorVariants[objectKey] = newProduct;
      }
    });
    finalProducts.push(...Object.values(colorVariants));
  });
  return finalProducts;
}
