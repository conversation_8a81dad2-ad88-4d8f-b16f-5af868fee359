import { expandVariantToProducts } from '../helper/product-variant';
import data from './data.json';
test('load variant to main Product', () => {
  //   console.log();
  const products = data.data.node.products.edges;
  const allProducts = expandVariantToProducts([products[0]]);
  console.log(allProducts.length);
  //   expandVariantToProducts
  // allProducts.map((product) => {
  //   console.log(product.node.title);
  //   console.log(product.node.images.edges[0].node.src);
  //   });
});
