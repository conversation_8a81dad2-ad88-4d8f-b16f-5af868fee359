import { appmaker } from '@appmaker-xyz/core';
import { helpers } from '@appmaker-xyz/core';
import { pdpBlocks } from './custom-page-blocks/pdp';
import { expandVariantToProducts } from './helper/product-variant';

const { findBlockIndex } = helpers;

const block = {
  name: 'appmaker/customise-shirt-widget',
  clientId: 'widget-customise-shirt',
  attributes: {},
};

const buttonBlock = {
  name: 'appmaker/confirm-fit-widget',
  clientId: 'widget-confirm-fit',
  attributes: {
    //  pluginSettings: "{{plugins['bombay-shirts-staging-168202'].settings}}",
    pluginSettings: `{{(plugins["bombay-shirts-staging-168202"] && plugins["bombay-shirts-staging-168202"].settings) || (plugins["bombay-shirts-164972"] && plugins["bombay-shirts-164972"].settings)}}`,
  },
};

export function addWidgetinOrderListPage() {
  appmaker.addFilter(
    'inapp-page-data-response',
    'response-data',
    (data, { pageId }) => {
      if (pageId === 'OrderList') {
        var foundIndex = data.blocks.findIndex(
          (x) => x.clientId === 'order-list-card',
        );
        data.blocks[foundIndex].attributes.featureImg =
          '{{ blockItem.node && blockItem.node.lineItems && blockItem.node.lineItems.edges[0] && blockItem.node.lineItems.edges[0].node && blockItem.node.lineItems.edges[0].node.variant ?  blockItem.node.lineItems.edges[0].node.variant.image.url : "https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/07/dbbb0c62-product-placeholder.png" }}';
      }
      return data;
    },
  );
}

export function addWidgetinCartPage() {
  appmaker.addFilter(
    'inapp-page-data-response',
    'response-data',
    (data, { pageId }) => {
      if (pageId === 'cartPageCheckout') {
        var foundIndex = data.blocks.findIndex(
          (x) => x.clientId === 'cart-card',
        );
        data.blocks[foundIndex].attributes.aditionalInfo =
          "<% const vendor = blockItem.node.variant.product.vendor; if(vendor=='Bombay Shirt Company') { %> <%= echo('Ship by ') %> <%= dayjs().add(plugins['bombay-shirts-164972'].settings.bsc_esimated_shipping_days,'day').format('MMM DD YYYY') %> <% }if(vendor=='cityof_') { %><%= echo('Ship by ') %> <%= dayjs().add(plugins['bombay-shirts-164972'].settings.cityof_esimated_shipping_days,'day').format('MMM DD YYYY') %> <% }if(vendor=='Korra') { %><%= echo('Ship by ') %> <%= dayjs().add(plugins['bombay-shirts-164972'].settings.korra_esimated_shipping_days,'day').format('MMM DD YYYY') %> <% }if(vendor=='Pause') { %><%= echo('Ship by ') %> <%= dayjs().add(plugins['bombay-shirts-164972'].settings.pause_esimated_shipping_days,'day').format('MMM DD YYYY') %>  <% } %>";
      }
      return data;
    },
  );
}

export function addOTPPhoneCustomValidation() {
  appmaker.addFilter(
    'otp-phone-validator',
    'otp-phone-validator-sub',
    (data) => {
      return (phone) => {
        if (phone.startsWith('+91')) {
          if (
            phone[3] === '6' ||
            phone[3] === '7' ||
            phone[3] === '8' ||
            phone[3] === '9'
          ) {
            return true;
          }
        }
        return false;
      };
    },
  );
}
