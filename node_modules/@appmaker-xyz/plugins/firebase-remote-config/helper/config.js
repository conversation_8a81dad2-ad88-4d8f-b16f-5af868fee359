import { appmaker } from '@appmaker-xyz/core';
import remoteConfig from '@react-native-firebase/remote-config';

export async function loadConfig() {
  await remoteConfig().setDefaults({
    appmaker_home_page_id: 'home',
  });
  const fetchedRemotely = await remoteConfig().fetchAndActivate();
  const appmaker_home_page_id = remoteConfig().getValue(
    'appmaker_home_page_id',
  );
  appmaker.addFilter('home-page-id', 'remote-config-id', () =>
    appmaker_home_page_id.asString(),
  );
}
