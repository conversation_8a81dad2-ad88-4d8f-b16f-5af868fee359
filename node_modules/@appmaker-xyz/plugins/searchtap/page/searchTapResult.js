// import ICONS from '../../ToolBarIcons';
import { Dimensions } from 'react-native';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;

const searchTapResult = {
  title: 'Product list',
  attributes: {
    contentContainerStyle: {
      backgroundColor: 'white',
      position: 'relative',
    },
  },
  blocks: [
    // Don't remove appmaker/text
    {
      name: 'appmaker/text',
      attributes: {
        // title: '{{blockItem.collection.title}}',
      },
    },
    {
      dependencies: {
        pageState: ['metaData'],
      },
      attributes: {
        customDataSource: {
          source: 'searchTap',
          // responseType: 'replace',
          attributes: {
            mapping: {
              // items: 'data.results',
              items: 'data.data.products.edges',
            },
            methodName: 'searchProductList',
            // params: '{{pageState.searchKey}}',
            // params: 'red',
            params: '{{currentAction.params.searchQuery}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
          dependencies: {
            pageState: ['searchKey'],
          },
        },
        surface: 'searchtap-product-list',
        hasPages: true,
        horizontal: false,
        gridViewListing: true,
      },
      clientId: 'searchtap-shopify-product-list',
      name: 'appmaker/shopify-product-list',
      innerBlocks: [],
    },
    // {
    //   dependencies: {
    //     pageState: ['metaData'],
    //     appStorageState: ['checkout'],
    //   },
    //   attributes: {
    //     template: 2, //new product list block
    //     loadingLayout: 'product-grid',
    //     wishList: false,
    //     savedItemIds: '{{(pageState.metaData.savedItem.data)}}',
    //     id: '{{blockItem.node.id}}',
    //     hasPages: true,
    //     appmakerAction: {
    //       productId:
    //         '{{base64.encode("gid://shopify/Product/" + blockItem.id)}}',
    //       action: 'OPEN_PRODUCT',
    //     },
    //     dataSource: {
    //       source: 'searchTap',
    //       // responseType: 'replace',
    //       attributes: {
    //         mapping: {
    //           items: 'data.results',
    //         },
    //         methodName: 'searchProductList',
    //         // params: '{{pageState.searchKey}}',
    //         // params: 'red',
    //         params: '{{currentAction.params.searchQuery}}',

    //       },
    //       repeatable: 'Yes',
    //       repeatItem: 'DataSource',
    //       dependencies: {
    //         pageState: ['searchKey'],
    //       },
    //     },
    //     cartQuantity:
    //       '{{getCartQty(appStorageState.checkout,blockItem.node.id)}}',
    //     title: '{{blockItem.title}}',
    //     numColumns: deviceRatio > 1.6 ? '2' : '3',
    //     gridViewListing: true,
    //     uri: '{{blockItem.smallImage}}',
    //     productType: '{{blockItem.hasMultiplePrice}}',
    //     in_stock: true,
    //     blockItem: '{{blockItem}}',
    //     availableForSale:
    //       '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    //     regularPrice: '{{blockItem.price}}',
    //     salePrice: '{{blockItem.discounted_price}}',
    //     onSale: '{{blockItem.price > blockItem.discounted_price}}',
    //     salePercentage:
    //       '<% if(  blockItem.price>blockItem.discounted_price) { %><%= parseInt(100-((blockItem.discounted_price/ blockItem.price)*100 ))%> <%="%"%>%><% } %>',
    //     thumbnail_meta: {
    //       height: '{{blockItem.images.height}}',
    //       width: '{{blockItem.images.width}}',
    //     },
    //     bestSeller: '{{blockItem.node.bestseller_tag}}',
    //     new_product: '{{blockItem.node.new_tag}}',
    //     brandColor: { primary: '#B6DCC9', secondary: '#E63F12' },
    //     saleBadgeStyle: {
    //       textStyle: {
    //         color: 'white',
    //       },
    //       containerStyle: {
    //         backgroundColor: '#E63F12',
    //       },
    //     },
    //     __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    //     show_last_few_remaining:
    //       '{{ blockItem.node.totalInventory < 5 && blockItem.node.totalInventory > 0 }}',
    //     last_few_remaining_text: 'Last Few Remaining',
    //   },
    //   name: 'appmaker/product-grid-item',
    //   innerBlocks: [],
    //   clientId: 'product-list',
    //   isValid: true,
    // },
  ],
  fixedFooter: {
    name: 'appmaker/floating-button',
    attributes: {
      __display:
        '{{checkIfTrueFalse(plugins.shopify.settings.enable_wishlist_floating_button)}}',
      iconName: 'heart',
      type: 'iconButton',
      visibilityStatus: true,
      appmakerAction: { action: 'OPEN_WISHLIST' },
    },
  },
  _id: 'productList',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  dataSource: {},
  // toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
  metaDataSource: {
    savedItem: {
      source: 'savedProducts',
      attributes: {
        transformKey: 'product-list-save-items',
        methodName: 'allItems',
        params: {},
      },
    },
  },
};
export default searchTapResult;
