const searchPage = {
  title: 'Search',
  attributes: {
    reloadOnFocus: true,
    // headerShown: false,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: '#fff',
    },
  },
  blocks: [
    {
      name: 'searchtap/search-bar',
      attributes: {
        label: 'Search Product',
        topBarView: false,
        debounceInterval: 500,
        name: 'searchKey',
        appmakerAction: {
          action: 'OPEN_SEARCHTAP_RESULT',
          params: {
            title: 'Result',
          },
        },
      },
    },
    {
      name: 'appmaker/actionbar',
      attributes: {
        __appmakerAttributes: {
          emptyViewAttribute: {
            showEmptyView: false,
          },
        },
        // __display:
        // '{{checkIfTrueFalse(plugins.shopify.settings.show_apply_coupon)}}',
        // name: 'appmaker/appmaker',
        title: '{{blockItem.title}}',
        // title: '{{pageState.searchKey}}',
        featureImg: '{{blockItem.smallImage}}',
        // featureImg: '{{blockItem.images[0].src}}',
        imageResize: 'contain',
        appmakerAction: {
          productId: '{{"gid://shopify/Product/" + blockItem.id}}',
          action: 'OPEN_PRODUCT',
        },
        rightIcon: 'arrow-up-right',
        dataSource: {
          source: 'searchTap',
          responseType: 'replace',
          attributes: {
            mapping: {
              items: 'data.results',
            },
            methodName: 'searchProduct',
            params: '{{pageState.searchKey}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
          dependencies: {
            pageState: ['searchKey'],
          },
        },
      },
      dependencies: {
        pageState: ['searchKey'],
      },
    },
  ],
};
export default searchPage;
