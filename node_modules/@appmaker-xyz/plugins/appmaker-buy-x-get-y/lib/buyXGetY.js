export function buyXGetY(freeGiftConfig) {
  return (input, dependencies) => {
    const {
      cartHelpers: { hasVariantInCart },
    } = dependencies;

    const freeGifts = freeGiftConfig.filter((gift) => gift.status);
    const freeGiftsToAdd = [];
    const freeGiftToRemove = [];

    freeGifts.forEach((gift) => {
      const { products_required, products_to_add } = gift;
      const productsRequiredCondition =
        gift.products_required_condition || 'all';

      const productsRequired = products_required.map(
        (product) => product.variant_gid,
      );

      // if products_required are in cart then add products_to_add
      let shoudAddProducts = false;
      if (productsRequiredCondition === 'all') {
        shoudAddProducts = productsRequired.every((product) =>
          hasVariantInCart(input, product),
        );
      } else if (productsRequiredCondition === 'any') {
        shoudAddProducts = productsRequired.some((product) =>
          hasVariantInCart(input, product),
        );
      }

      if (shoudAddProducts) {
        products_to_add.forEach((product) => {
          if (!hasVariantInCart(input, product.variant_gid)) {
            freeGiftsToAdd.push({
              variantId: product.variant_gid,
              quantity: 1,
              customAttributes: [
                {
                  key: 'appmaker_buy_x_get_y',
                  value: 'Buy X Get Y',
                },
                ...(gift?.attributes || []),
              ],
            });
          }
        });
      } else {
        products_to_add.forEach((product) => {
          const lineItem = hasVariantInCart(input, product.variant_gid);
          if (lineItem) {
            freeGiftToRemove.push({
              id: lineItem.node.id,
              variantId: product.variant_gid,
              quantity: 0,
            });
          }
        });
      }
    });

    input.lineItemsToAdd = input.lineItemsToAdd
      ? [...input.lineItemsToAdd, ...freeGiftsToAdd]
      : freeGiftsToAdd;

    input.lineItemsToUpdate = input.lineItemsToUpdate
      ? [...input.lineItemsToUpdate, ...freeGiftToRemove]
      : freeGiftToRemove;

    return input;
  };
}
