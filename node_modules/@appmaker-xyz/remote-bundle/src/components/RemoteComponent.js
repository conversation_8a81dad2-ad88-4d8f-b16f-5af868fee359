import React, { useState } from 'react';
import { useRemoteComponent } from '../hooks/useRemoteComponent';
import { ErrorBoundary } from './ErrorBoundary';
import { Button } from 'react-native';
// import { Button } from '@appmaker-xyz/ui';
import { applyFilters } from '@appmaker-xyz/core';

export default function RemoteComponent({ url, componentName, props = {} }) {
  const [reloadKey, setReloadKey] = useState(0);

  const Component = useRemoteComponent({
    url,
    moduleName: componentName,
    reloadKey,
  });

  const handleReload = () => {
    setReloadKey((prevKey) => prevKey + 1);
  };
  const isDeveloperModeActive = applyFilters('is_developer_mode_active', false);
  return (
    <ErrorBoundary>
      {Component !== null ? <Component {...props} /> : null}
      {isDeveloperModeActive ? (
        <Button title="Reload" onPress={handleReload} />
      ) : null}
    </ErrorBoundary>
  );
}
