import React from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import { loadBundle } from '../core';

export function useRemoteComponent({ url, moduleName, reloadKey }) {
  const ComponentRef = React.useRef(null);
  const [loadCount, setLoadCount] = useState(0);

  useEffect(() => {
    async function _loadBundle() {
      const _component = await loadBundle({
        url,
        bundleId: `${url}_${reloadKey}`,
        debug: true,
      });
      if (_component[moduleName]) {
        ComponentRef.current = _component[moduleName];
        setLoadCount((count) => count + 1);
      }
    }

    if (url) {
      _loadBundle();
    }
  }, [url, moduleName, reloadKey]);

  return ComponentRef.current;
}
