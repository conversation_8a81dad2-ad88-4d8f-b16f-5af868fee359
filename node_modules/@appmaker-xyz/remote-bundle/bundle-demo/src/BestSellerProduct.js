import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';

const BestSellerProduct = () => {
  const imageSource = {
    uri: 'https://cdn.shopify.com/s/files/1/0688/1755/1382/products/GreenMenscrew01_400x400_crop_center.jpg?v=1675454919',
  };
  const regularPrice = 50.00;
  const salePrice = 40.00;
  const savingsPercentage = ((regularPrice - salePrice) / regularPrice) * 100;
  const qtySold = 1000;
  const salesEnd = new Date(Date.now() + (20 * 60 * 60 * 1000)); // 20 hours from now

  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image source={imageSource} style={styles.image} />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>Best-Selling Product</Text>
        <Text style={styles.regularPrice}>${regularPrice.toFixed(2)}</Text>
        <Text style={styles.salePrice}>${salePrice.toFixed(2)}</Text>
        <Text style={styles.savings}>Save {savingsPercentage.toFixed(0)}%</Text>
        <Text style={styles.qtySold}>Sold: {qtySold}</Text>
        <Text style={styles.salesEnd}>Ends in {Math.floor((salesEnd - Date.now()) / (1000 * 60 * 60))} hours</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 20,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  imageContainer: {
    width: 120,
    height: 120,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    padding: 10,
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  regularPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
    color: '#999999',
    marginBottom: 5,
  },
  salePrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ff5555',
    marginBottom: 5,
  },
  savings: {
    fontSize: 16,
    color: '#ff5555',
    marginBottom: 5,
  },
  qtySold: {
    fontSize: 16,
    color: '#999999',
    marginBottom: 5,
  },
  salesEnd: {
    fontSize: 16,
    color: '#999999',
  },
});

export default BestSellerProduct;
