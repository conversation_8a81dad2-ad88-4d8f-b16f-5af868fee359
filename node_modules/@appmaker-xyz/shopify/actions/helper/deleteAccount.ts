import { Platform } from 'react-native';

import axios from 'axios';
// @ts-ignore
import store from 'react-native-simple-store';
import { getProjectId } from '@appmaker-xyz/core';

const BASE_URL = 'https://shopify-store-api.appmaker.xyz';
const APPMAKER_APP_VERSION = '_appmaker_app_version';

/**
 * Deletes a user account from the Shopify store.
 *
 * @param {string} email - The email of the user whose account is to be deleted.
 * @param {string} accessToken - The access token of the user.
 * @returns {Promise<Object>} The response from the server.
 */
export const deleteAccount = async ({
  email,
  accessToken,
  storeUrl,
  storeAccessToken,
}: {
  email: string;
  accessToken: string;
  storeUrl: string;
  storeAccessToken: string;
}) => {
  if (!email || !accessToken || !storeUrl || !storeAccessToken) {
    return {
      error: 'Invalid input',
    };
  }
  const localAppVersion = await store.get(APPMAKER_APP_VERSION);
  const url = `${BASE_URL}/account-delete`;
  const headers = {
    'Content-Type': 'application/json',
    'x-appmaker-project-id': getProjectId(),
    'x-shopify-storefront-access-token': storeAccessToken,
  };
  const data = {
    customerAccessToken: accessToken,
    shopUrl: storeUrl,
    email: email,
    platform: Platform.OS,
    appVersion: localAppVersion,
  };
  const genericError = {
    error: 'Something went wrong',
  };
  try {
    return await axios.post(url, data, { headers });
  } catch (error) {
    console.log('Error deleting account', error);
    return genericError;
  }
};
