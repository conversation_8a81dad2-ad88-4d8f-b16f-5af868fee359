import {
  CartGiftCardCodesRemoveDocument,
  CartGiftCardCodesRemoveMutation,
  CartGiftCardCodesRemoveMutationVariables,
  useCartGiftCardCodesRemoveMutation,
} from '../../../datasource/api-react-query';
import { runTanstackQueryMutation } from '../../../datasource/fetcher';
import { getCart, updateCartPageState, validateAndSaveCart } from '../helper';

type CartGiftCardCodesRemoveOptions = {
  ignoreCartValidateAndSave?: boolean;
  updateCartPageStateRequired?: boolean;
};

export const cartGiftCardCodesRemove = async (
  appliedGiftCardIds: string[],
  options: CartGiftCardCodesRemoveOptions = {},
) => {
  const currentCart = getCart();
  const cartId = currentCart?.id;
  if (!cartId) {
    return { error: 'Error: Cart ID not found' };
  }
  try {
    const variables: CartGiftCardCodesRemoveMutationVariables = {
      appliedGiftCardIds,
      cartId,
    };
    const response = await runTanstackQueryMutation<
      CartGiftCardCodesRemoveMutation,
      CartGiftCardCodesRemoveMutationVariables
    >(
      CartGiftCardCodesRemoveDocument,
      variables,
      useCartGiftCardCodesRemoveMutation.getKey(),
    );
    if (!options?.ignoreCartValidateAndSave) {
      await validateAndSaveCart(response);
    }
    if (options?.updateCartPageStateRequired) {
      updateCartPageState(response?.cartGiftCardCodesRemove?.cart);
    }
    return {
      cart: response?.cartGiftCardCodesRemove?.cart,
      userErrors: response?.cartGiftCardCodesRemove?.userErrors || [],
    };
  } catch (error) {
    return {
      cart: null,
      error: error,
    };
  }
};
