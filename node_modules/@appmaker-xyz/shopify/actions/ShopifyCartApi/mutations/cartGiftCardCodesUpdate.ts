import {
  CartGiftCardCodesUpdateMutationVariables,
  CartGiftCardCodesUpdateMutation,
  CartGiftCardCodesUpdateDocument,
  useCartGiftCardCodesUpdateMutation,
} from '../../../datasource/api-react-query';
import { runTanstackQueryMutation } from '../../../datasource/fetcher';
import { getCart, updateCartPageState, validateAndSaveCart } from '../helper';

type CartGiftCardCodesUpdateOptions = {
  ignoreCartValidateAndSave?: boolean;
  updateCartPageStateRequired?: boolean;
};
export const cartGiftCardCodesUpdate = async (
  giftCardCodes: string[],
  options: CartGiftCardCodesUpdateOptions = {},
) => {
  const currentCart = getCart();
  const cartId = currentCart?.id;
  if (!cartId) {
    return { error: 'Error: Cart ID not found' };
  }
  try {
    const variables: CartGiftCardCodesUpdateMutationVariables = {
      cartId,
      giftCardCodes,
    };
    const response = await runTanstackQueryMutation<
      CartGiftCardCodesUpdateMutation,
      CartGiftCardCodesUpdateMutationVariables
    >(
      CartGiftCardCodesUpdateDocument,
      variables,
      useCartGiftCardCodesUpdateMutation.getKey(),
    );
    if (!options?.ignoreCartValidateAndSave) {
      await validateAndSaveCart(response);
    }
    if (options?.updateCartPageStateRequired) {
      updateCartPageState(response?.cartGiftCardCodesUpdate?.cart);
    }
    return {
      cart: response?.cartGiftCardCodesUpdate?.cart,
      userErrors: response?.cartGiftCardCodesUpdate?.userErrors || [],
    };
  } catch (error) {
    return {
      cart: null,
      error: error,
    };
  }
};
