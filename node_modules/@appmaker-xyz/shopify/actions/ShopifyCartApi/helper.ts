import { appStorageApi, pageStateApi, appSettings } from '@appmaker-xyz/core';
const CART_STORAGE_KEY = 'shopifyCart';
const CART_STORAGE_KEY_LEGACY = 'checkout';
const CART_RESTORE_STORAGE_KEY = 'shopifyCartRestore';
import { shopifyCartToLegacyCart } from '../../helper/helper';
import { getAllCartUserErrorHandlers } from './userErrorHandlers';
export const getLanguage = () => {
  try {
    const language = (
      appStorageApi()?.getState()?.language?.toUpperCase?.() ||
      appSettings?.getOption('defaultLanguage', 'EN')
    )?.toUpperCase?.();
    return language;
  } catch (e) {
    return 'EN';
  }
};

export const updateCartPageState = (cart) => {
  if (cart?.id) {
    pageStateApi().setState({
      blockData: cart,
    });
  }
};
export const saveCart = (cart) => {
  appStorageApi().setState({
    [CART_STORAGE_KEY]: cart,
  });
  // save the cart in the legacy storage key
  const cartLegacy = shopifyCartToLegacyCart(cart);
  appStorageApi().setState({
    [CART_STORAGE_KEY_LEGACY]: cartLegacy,
  });
};
export const clearCart = () => {
  appStorageApi().setState({
    [CART_STORAGE_KEY]: null,
  });
  appStorageApi().setState({
    [CART_STORAGE_KEY_LEGACY]: null,
  });
};
export const saveRestoreCart = (cart) => {
  appStorageApi().setState({
    [CART_RESTORE_STORAGE_KEY]: cart,
  });
};
export const clearRestoreCart = () => {
  appStorageApi().setState({
    [CART_RESTORE_STORAGE_KEY]: null,
  });
};

export const getCart = () => {
  return appStorageApi().getState()[CART_STORAGE_KEY];
};


export const handleCartErrors = async (userErrors: any) => {
  try {
    const errorHandlers = getAllCartUserErrorHandlers();
    if (userErrors?.length > 0) {
      for (const errorHandler of errorHandlers) {
        if (errorHandler?.if?.(userErrors)) {
          return await errorHandler?.handler?.(userErrors);
        }
      }
      return {
        errors: userErrors,
      };
    }
  } catch (e) {
    console.error('[Shopify] Error in handling cart errors - ', { e });
  }
};
export const validateAndSaveCart = async (
  cartResponse: any,
  deleteEmptyCart: boolean = false,
) => {
  if (!cartResponse) return null;
  let isCartValid = true;
  const cart =
    cartResponse?.cartNoteUpdate ||
    cartResponse?.cartAttributesUpdate ||
    cartResponse?.cartLinesUpdate ||
    cartResponse?.cartLinesRemove ||
    cartResponse?.cartDiscountCodesUpdate ||
    cartResponse?.cartLinesAdd ||
    cartResponse?.cartBuyerIdentityUpdate ||
    cartResponse?.cartGiftCardCodesUpdate ||
    cartResponse?.cartGiftCardCodesRemove;
  try {
    const shouldDeleteCart =
      deleteEmptyCart &&
      (cartResponse?.cartLinesUpdate || cartResponse?.cartLinesRemove) &&
      cart?.cart?.lines?.nodes?.length === 0;
    if (shouldDeleteCart) {
      // clear cart
      clearCart();
      return { cart: cart, isCartValid: true };
    }


    // TODO: move this to error handlers. 
    if (cart?.userErrors?.length > 0) {
      // check if the cart response has an error with code INVALID and field cartId
      const invalidCartError = cart?.userErrors?.find(
        (error: any) =>
          error?.code === 'INVALID' && error?.field?.includes?.('cartId'),
      );

      // Clear cart if cart id is invalid.
      if (invalidCartError) {
        console.log(
          `[Shopify] validateAndSaveCart. Invalid Cart error. `,
          invalidCartError,
        );
        isCartValid = false;
        // clear the cart
        const currentCart = appStorageApi().getState()[CART_STORAGE_KEY] || {};
        saveRestoreCart(currentCart);
        clearCart();
        return { cart: null, isCartValid };
      }
    }

    cart?.cart && saveCart(cart?.cart);

    return { cart, isCartValid };
  } catch (e) {
    console.error('[Shopify] Error validating and saving cart - ', { e });
    return { cart: null, isCartValid: true };
  }
};