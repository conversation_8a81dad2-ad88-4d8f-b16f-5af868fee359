import { appmaker } from '@appmaker-xyz/core';
import { handleAction } from '@appmaker-xyz/react-native';
import { CartUserError } from '../../datasource/api-react-query';

type CartUserErrors = Array<CartUserError>;
type CartUserErrorHandler = {
  key: string;
  if: (userErrors: CartUserErrors) => boolean;
  handler: (userErrors: CartUserErrors) => Promise<any>;
};

const isUserAccessTokenInvalid = (userErrors: any) => {
  return userErrors?.find(
    (error: CartUserError) =>
      error?.code === 'INVALID' &&
      error?.field?.includes?.('input', 'buyerIdentity', 'customerAccessToken'),
  );
};
const userErrorHandlers: CartUserErrorHandler[] = [
  {
    key: 'invalid-user-access-token',
    if: (userErrors: CartUserErrors) => isUserAccessTokenInvalid(userErrors),
    handler: async (userErrors: CartUserErrors) => {
      await handleAction({
        action: 'CHECK_LOGIN_EXPIRED',
      });
      await handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Your session has expired. Please login again.',
        },
      });
      return {
        errors: [
          {
            message: 'Your session has expired. Please login again.',
          },
        ],
      };
    },
  },
];

export const getAllCartUserErrorHandlers = (): CartUserErrorHandler[] => {
  return appmaker.applyFilters(
    'cart-user-error-handlers',
    userErrorHandlers,
  ) as CartUserErrorHandler[];
};

export default userErrorHandlers;
