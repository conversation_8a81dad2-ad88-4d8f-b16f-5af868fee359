const page = {
  blocks: [
    {
      name: 'appmaker/order-detail-header',
      clientId: 'ProductListPage',
      context: true,
      attributes: {
        context: true,
        data: '{{blockData[0]}}',
        orderId: '{{blockData[0].node.name.replace("#","")}}',
        topNotice: '{{[]}}',
        can_repeat_order: '{{false}}',
        paymentMethod: 'payment',
        status: 'processing',
        orderDate: "{{momentJS(blockItem.node.processedAt,'MMM Do YY')}}",
        numberOfItems: '{{blockData[0].node.lineItems.edges.length}}',
        canCancelOrder: '{{false}}',
      },
    },
    {
      name: 'appmaker/card',
      clientId: 'ProductListPage',
      context: true,
      attributes: {
        hasPages: false,
        context: true,
        type: 'type-2',
        featureImg: '{{blockItem.node.variant.image.url}}',
        title: '{{blockItem.node.title}} ({{blockItem.node.variant.title}})',
        excerpt:
          '{{currencyHelper(blockItem.node.variant.price.amount,blockItem.node.variant.price.currencyCode)}} x {{blockItem.node.quantity}}',
        data: '{{blockItem[0]}}',
        dataSource: {
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData[0].node.lineItems.edges}}',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
      },
    },
    {
      name: 'appmaker/table-cell',
      attributes: {
        title: 'Total Amount',
        value:
          '{{currencyHelper(blockData[0].node.totalPrice.amount,blockData[0].node.totalPrice.currencyCode)}}',
      },
    },
  ],
  dataSource: {
    source: 'shopify',
    attributes: {
      params: '{{currentAction.params}}',
      mapping: {
        items: 'data.data.customer.orders.edges',
      },
      methodName: 'recentOrder',
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
  attributes: {
    showLoadingTillData: true,
    // renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      // paddingHorizontal: 16,
    },
  },
  title: 'Order Details',
};
export default page;
