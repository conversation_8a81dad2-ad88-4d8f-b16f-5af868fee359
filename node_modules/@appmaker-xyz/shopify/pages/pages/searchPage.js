import { spacing, color } from '../styles';
import z from 'zod';
const validQueryParamsSchema = z.string().min(1);
const searchPage = {
  title: 'Search',
  attributes: {
    // headerShown: false,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: color.light,
    },
  },
  blocks: [
    {
      name: 'appmaker/SearchBar',
      attributes: {
        label: 'Search Product',
        topBarView: false,
        debounceInterval: 500,
        name: 'searchKey',
        appmakerAction: {
          action: 'LIST_PRODUCT',
          params: {
            showSearch: true,
            replacePage: true,
            title: 'Result',
          },
        },
      },
    },
    {
      name: 'appmaker/actionbar',
      attributes: {
        // name: 'appmaker/appmaker',
        title: '{{blockItem.node.title}}',
        // title: '{{pageState.searchKey}}',
        featureImg: '{{blockItem.node.images.edges[0].node.url}}',
        imageResize: 'contain',
        appmakerAction: {
          productId: '{{blockItem.node.id}}',
          // params: {
          // pageData: '{{blockItem}}',
          // },
          action: 'OPEN_PRODUCT',
        },
        __appmakerAttributes: {
          emptyViewAttribute: {
            showEmptyView: false,
          },
        },
        rightIcon: 'arrow-up-right',
        dataSource: {
          source: 'shopify',
          responseType: 'replace',
          attributes: {
            mapping: {
              items: 'data.data.products.edges',
            },
            methodName: 'searchProduct',
            params: '{{pageState.searchKey}}',
            paramsValidations: {
              zodSchema: validQueryParamsSchema,
            },
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
          dependencies: {
            pageState: ['searchKey'],
          },
        },
      },
      dependencies: {
        pageState: ['searchKey'],
      },
    },
    // {sa
    //   name: 'appmaker/SearchSuggestion',
    //   contextValues: true,
    //   attributes: {
    //     horizontal: true,
    //     contextValues: {
    //       searchSuggestion: 'searchSuggestion',
    //     },
    //   },
    // },
    // {
    //   name: 'appmaker/block-card',
    //   attributes: {
    //     title: 'Search Suggestions',
    //     childContainerStyle: {
    //       paddingBottom: spacing.base,
    //       flexDirection: 'row',
    //     },
    //     rootContainerStyle: {
    //       flex: 1,
    //     },
    //     contentContainerStyle: {
    //       flex: 1,
    //     },
    //   },
    //   innerBlocks: [
    //     {
    //       name: 'appmaker/SearchSuggestion',
    //       contextValues: true,
    //       attributes: {
    //         horizontal: true,
    //         contextValues: {
    //           searchSuggestion: 'searchSuggestion',
    //         },
    //       },
    //     },
    //   ],
    // },
  ],
};
export default searchPage;
