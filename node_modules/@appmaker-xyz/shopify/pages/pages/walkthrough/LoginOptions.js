import { color, spacing } from '../../styles';
import {
  colors as configColors,
} from '@appmaker-xyz/app-config/newConfig';

import { appSettings } from '@appmaker-xyz/core';

const LoginOptions = {
  type: 'normal',
  title: ' ',
  attributes: {
    headerShown: true,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  },
  blocks: [
    {
      name: 'appmaker/remoteImage',
      attributes: {
        name: 'LOGIN_LOGO',
        resizeMode: 'contain',
        style: {
          width: 250,
          height: 100,
          marginBottom: spacing.xl,
        },
      },
    },
    {
      name: 'appmaker/layout',
      clientId: 'login-layout',
      attributes: {
        style: {
          width: '100%',
        },
      },
      innerBlocks: [
        // {
        //   name: 'appmaker/actionbar',
        //   attributes: {
        //     featureImg:
        //       'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/23f54b93-image-2.png',
        //     imageResize: 'contain',
        //     title: 'Continue with Facebook',
        //     loading: false,
        //     fontColor: '#ffffff',
        //     containerStyle: {
        //       backgroundColor: '#1877F2',
        //       marginHorizontal: spacing.base,
        //       marginBottom: spacing.mini,
        //       borderRadius: 0,
        //     },
        //   },
        // },
        // {
        //   name: 'appmaker/actionbar',
        //   attributes: {
        //     featureImg:
        //       'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/d45ea601-image-5.png',
        //     imageResize: 'contain',
        //     title: 'Continue with Google',
        //     loading: false,
        //     fontColor: '#ffffff',
        //     containerStyle: {
        //       backgroundColor: '#0099FF',
        //       marginHorizontal: spacing.base,
        //       marginBottom: spacing.mini,
        //       borderRadius: 0,
        //     },
        //   },
        // },
        // {
        //   name: 'appmaker/actionbar',
        //   attributes: {
        //     featureImg:
        //       'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/2513ae19-image-4.png',
        //     imageResize: 'contain',
        //     title: 'Continue with Apple',
        //     loading: false,
        //     fontColor: '#ffffff',
        //     containerStyle: {
        //       backgroundColor: '#000000',
        //       marginHorizontal: spacing.base,
        //       marginBottom: spacing.mini,
        //       borderRadius: 0,
        //     },
        //   },
        // },
        // {
        //   name: 'appmaker/actionbar',
        //   attributes: {
        //     featureImg:
        //       'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/985852b9-otp-1.png',
        //     imageResize: 'contain',
        //     title: 'Continue with Phone Number',
        //     loading: false,
        //     fontColor: '#ffffff',
        //     containerStyle: {
        //       backgroundColor: '#4F4F4F',
        //       marginHorizontal: spacing.base,
        //       marginBottom: spacing.mini,
        //       borderRadius: 0,
        //     },
        //     appmakerAction: {
        //       action: 'OPEN_INAPP_PAGE',
        //       pageId: 'CustomOTPLogin',
        //     },
        //   },
        // },
        {
          name: 'appmaker/actionbar',
          attributes: {
            testId: 'signup-option',
            featureImg: 'https://app-static-pages.appmaker.xyz/images/mail.png',
            imageResize: 'contain',
            title: 'Continue with Email',
            loading: false,
            fontColor: '#ffffff',
            containerStyle: {
              backgroundColor: '#A9AEB7',
              marginHorizontal: spacing.base,
              marginBottom: spacing.mini,
              borderRadius: 0,
            },
            appmakerAction: {
              action: 'OPEN_INAPP_PAGE',
              pageId: 'Register',
            },
          },
        },
        {
          name: 'appmaker/actionbar',
          attributes: {
            testId: 'login-option',
            title: 'Login to an existing account',
            loading: false,
            containerStyle: {
              backgroundColor: '#ffffff',
              marginHorizontal: spacing.base,
              marginBottom: spacing.mini,
              borderRadius: 0,
            },
            appmakerAction: {
              action: 'OPEN_INAPP_PAGE',
              pageId: 'LoginPage',
            },
          },
        },
      ],
    },
  ],
};
export default LoginOptions;
