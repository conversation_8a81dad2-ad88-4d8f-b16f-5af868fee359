import { color, spacing } from '../../styles';

const OtpLoginVerify = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    headerShown: false,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
      paddingHorizontal: spacing.base,
    },
  },
  blocks: [
    {
      name: 'appmaker/appImage',
      attributes: {
        uri: 'https://storage.googleapis.com/site-cdn.appmaker.xyz/2022/06/6ecad864-peachmode_logo_390x.webp',
        resizeMode: 'contain',
        style: {
          width: 250,
          height: 100,
          marginTop: spacing.xl,
          marginBottom: spacing.lg,
        },
      },
    },
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          marginBottom: spacing.lg,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Login with OTP',
            category: 'h1Heading',
          },
        },
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Enter OTP send to +91-9999-777-888',
            category: 'bodySubText',
          },
        },
      ],
    },
    {
      name: 'appmaker/floating-label-input',
      attributes: {
        label: 'Enter OTP',
        type: 'password',
      },
    },
    {
      name: 'appmaker/button',
      attributes: {
        content: 'Verify',
        status: 'dark',
        baseSize: true,
        wholeContainerStyle: {
          borderRadius: 40,
          marginTop: spacing.base,
          backgroundColor: '#ee3d63',
          borderWidth: 0,
        },
        appmakerAction: {
          action: 'OPEN_INAPP_PAGE',
          pageId: 'BasicDetails',
        },
      },
    },
  ],
};
export default OtpLoginVerify;
