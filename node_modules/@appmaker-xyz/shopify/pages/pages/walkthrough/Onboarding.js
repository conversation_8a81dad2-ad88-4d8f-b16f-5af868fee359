import { color } from '../../styles';
import { appSettings } from '@appmaker-xyz/core';

const Onboarding = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    headerShown: false,
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/onboarding',
      attributes: {
        // text: 'A style for the individual personality, Well designed, well presented.',
        skippable: !appSettings.getExtensionOptionAsBoolean(
          'app-settings',
          'app_force_login',
          false,
        ),
        buttonColor: '#000000',
        roundedButton: true,
        skipAction: {
          action: 'ONBOARDING_SKIP',
        },
        appmakerAction: {
          action: 'OPEN_INAPP_PAGE',
          pageId: 'LoginOptions',
        },
      },
    },
  ],
};
export default Onboarding;
