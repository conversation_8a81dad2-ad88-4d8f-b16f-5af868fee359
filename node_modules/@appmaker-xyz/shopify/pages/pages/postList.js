import ICONS from '../../ToolBarIcons';

const pagesData = {
  title: 'Blog Posts',
  blocks: [
    {
      clientId: 'my-account-menu',
      name: 'appmaker/post-item',
      attributes: {
        id: '{{blockItem.node.id}}',
        containerStyle: {
          flex: 1,
        },
        title: '{{blockItem.node.title}}',
        featureImg:
          '<% if(blockItem.node.image){echo(blockItem.node.image.src)}else{echo(null)} %>',
        excerpt:
          '<% if(blockItem.node.excerptHtml){echo(blockItem.node.excerptHtml)}else{echo(null)} %>',
        meta: "{{[{iconName:'user',title:blockItem.node.author.name},{iconName:'clock',title:momentJS(blockItem.node.publishedAt,'MMM Do YY')}]}}",
        contentHtml: '{{blockItem.node.contentHtml}}',
        blockItem: '{{blockItem}}',
        __appmakerAttributes: {
          errorViewAttribute: {
            message: 'Something went wrong',
          },
          emptyViewAttribute: {
            message: 'There are no articles here',
          },
        },
        appmakerAction: {
          params: {
            pageData: '{{blockItem}}',
          },
          action: 'OPEN_POST_DETAIL_PAGE',
        },
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.blog.articles.edges',
            },
            methodName: 'post',
            params: '{{currentAction.params}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
      },
    },
  ],
  _id: 'postList',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  dataSource: {},
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default pagesData;
