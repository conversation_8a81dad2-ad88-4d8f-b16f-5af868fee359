import ICONS from '../../ToolBarIcons';
import { appSettings } from '@appmaker-xyz/core';

const pagesData = {
  _id: 'pageDetail',
  status: 'active',
  title: ' ',
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    contentContainerStyle: {
      flex: 1,
    },
    rootContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        __display: '<%=isBlank(blockData.data.data.page.body)%>',
        url: '{{blockData.data.data.page.url}}?from_mobile_app=1',
        source: {
          uri: '{{blockData.data.data.page.url}}?from_mobile_app=1',
        },
      },
    },
    {
      name: 'appmaker/webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        __display: '<%=!isBlank(blockData.data.data.page.body)%>',
        url: '',
        source: {
          html: `<meta name="viewport" content="width=device-width, initial-scale=1.0"><style type="text/css">${appSettings.getOption(
            'shopify_product_description_css',
          )}</style>{{blockData.data.data.page.body}}`,
        },
      },
    },
  ],
  dataSource: {
    source: 'shopify',
    attributes: {
      // mapping: {
      //   items: 'data.data.page',
      // },
      methodName: 'page',
      params: '{{currentAction.params}}',
    },
  },
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default pagesData;
