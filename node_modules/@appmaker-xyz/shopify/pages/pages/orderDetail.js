const page = {
  blocks: [
    {
      name: 'shopify/order-detail-header',
      clientId: 'order-detail-header',
      attributes: {
        data: '{{blockData}}',
        orderId: '{{blockItem.node.name.replace("#","")}}',
        orderDate: "{{momentJS(blockItem.node.processedAt,'MMM Do YY')}}",
        topNotice: '{{[]}}',
        can_repeat_order: '{{false}}',
        status: '{{blockItem.node.fulfillmentStatus}}',
        paymentMethod: '{{blockItem.node.financialStatus}}',
        orderFulfillment: '{{blockItem.node.successfulFulfillments}}',
        numberOfItems: '{{blockItem.node.lineItems.edges.length}}',
        canCancelOrder: '{{false}}',
      },
    },
    {
      name: 'shopify/fulfillment-status',
      clientId: 'order-detail-fulfillment-status',
      attributes: {},
    },
    {
      clientId: '143f1163-10ac-4b22-bf57-2ba78c94b551',
      name: 'shopify/order-line-items',
    },
    {
      name: 'shopify/shipping-address',
      clientId: 'order-detail-shipping-address',
      attributes: {},
    },
    {
      clientId: '143f1163-10ac-4b22-bf57-2ba78c94b516',
      name: 'shopify/order-detail-price-table',
    },
  ],
  attributes: {
    showLoadingTillData: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  title: ' ',
};
export default page;
