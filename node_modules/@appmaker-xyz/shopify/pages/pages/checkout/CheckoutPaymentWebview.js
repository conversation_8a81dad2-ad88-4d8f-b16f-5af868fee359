import { getActionFromURL } from '../../../helper/deeplink';
import { shopConfig } from '@appmaker-xyz/shopify/store/shopifyStore';
import {
  getShopifyBaseUrl,
  isOrderCompleted,
  webviewDeepLinkEnabled,
} from '../../../helper/helper';
const onUrlChange = async (url, onAction) => {
  const shopData = shopConfig.get();
  const homePageURL = shopData?.shop?.primaryDomain?.url;
  const isMatch = url === homePageURL || url === `${homePageURL}/`;
  if (isMatch) {
    onAction({
      action: 'GO_TO_HOME',
    });
  }
  if (isOrderCompleted(url)) {
    onAction({
      action: 'SET_ORDER_COMPLETE',
    });
  }
  if (webviewDeepLinkEnabled()) {
    const action = await getActionFromURL(url, getShopifyBaseUrl());
    if (action) {
      return onAction(action);
    }
  }
};
// const styles = ".content-box__row.content-box__row--tight-spacing-vertical,.section.section--billing-address,.section.section--reductions.hidden-on-desktop,a.step__footer__previous-link,button.order-summary-toggle.order-summary-toggle--show.shown-if-js,header.banner,header.main__header{display:none}"
const styles =
  'button#continue_button{position:fixed;bottom:0;left:0;border-radius:0!important}';
const customStyles = `
function injectStyles(inlineStyle) {
  const style = document.createElement('style');
  style.innerHTML = inlineStyle;
  document.head.appendChild(style);
}
injectStyles("${styles}")
`;
const page = {
  id: 'checkout',
  status: 'active',
  title: 'Checkout',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/shopify-payment-webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        // injectedJavaScript: `
        // // document.querySelector("#continue_button").click();
        // document.querySelector("[data-payment-subform]").scrollIntoView()
        // ${customStyles}
        // `,
        loadingLayout: 'home',
        urlListener: onUrlChange,
        source: '{{blockData.source}}',
      },
      dependencies: {
        appStorageState: ['checkout'],
      },
    },
  ],
};
export default page;
