import { color, spacing } from '../../styles';

const pickMethod = {
  type: 'normal',
  title: 'Shipping Method',
  attributes: {
    renderType: 'normal',
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/text',
      attributes: {
        content: 'SHIPPING OPTIONS',
        category: 'bodyParagraph',
        status: 'demiDark',
        style: {
          padding: spacing.base,
        },
      },
    },
    {
      name: 'appmaker/form-radio-item',
      attributes: {
        name: 'shipping_method',
        id: '{{blockItem.handle}}',
        label: '{{blockItem.title}}',
        additionalText:
          '{{ parseInt(blockItem.price.amount) === 0 ?  "Free" : currencyHelper(blockItem.price.amount, blockItem.price.currencyCode)}}',
        dataSource: {
          source: 'shopify',
          attributes: {
            mapping: {
              items: 'data.data.node.availableShippingRates.shippingRates',
            },
            methodName: 'availableShippingRates',
            params: '{{appStorageStateApi.checkout.id}}',
          },
          repeatable: 'Yes',
          repeatItem: 'DataSource',
        },
      },
    },
    // {
    //   name: 'appmaker/form-radio-item',
    //   attributes: {
    //     name: 'shipping_method',
    //     id: 'same-day',
    //     label: 'Same Day Shipping',
    //     additionalText: '$9.00',
    //   },
    // },
  ],
  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/button',
    attributes: {
      appmakerAction: {
        action: 'SET_SHIPPING_METHOD',
      },
      content: 'Continue to payment',
      wholeContainerStyle: {
        borderRadius: 0,
      },
      status: 'dark',
    },
  },
};

export default pickMethod;
