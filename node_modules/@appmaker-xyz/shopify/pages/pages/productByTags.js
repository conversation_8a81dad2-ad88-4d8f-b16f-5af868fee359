import { helpers } from '@appmaker-xyz/core';
import productList from './productList';

const { findBlockIndex } = helpers;

let pageData = JSON.parse(JSON.stringify({ ...productList, title: 'By Tags' }));
const productListClientId = findBlockIndex(pageData.blocks, 'product-list');

pageData.blocks[productListClientId].attributes.dataSource = {
  source: 'shopify',
  attributes: {
    mapping: {
      items: 'data.data.products.edges',
    },
    methodName: 'products',
    params: '{{currentAction.params}}',
  },
  repeatable: 'Yes',
  repeatItem: 'DataSource',
};
export default pageData;
