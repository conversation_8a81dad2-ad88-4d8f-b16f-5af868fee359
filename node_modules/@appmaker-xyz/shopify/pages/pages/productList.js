import ICONS from '../../ToolBarIcons';
const pagesData = {
  title: 'Product list',
  attributes: {
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: 'white',
    },
  },
  blocks: [
    {
      name: 'appmaker/banner',
      attributes: {
        __display:
          '{{checkIfTrueFalse(plugins.shopify.settings.show_collection_banner)}}',
        image: '{{ {url:blockItem.collection.image.url} }}',
        uri: '{{blockItem.collection.image.url}}',
        thumbnail_meta: '{{blockItem.collection.image}}',
        viewSingle: true,
      },
    },
    {
      name: 'appmaker/page-head',
      attributes: {
        title: '{{blockItem.collection.title}}',
        layoutSelector: false, //TODO: Layout selector should be fixed
        viewSingle: true,
        __display: '{{plugins.shopify.settings.show_collection_title}}',
      },
    },
    {
      attributes: {},
      name: 'appmaker/shopify-product-list',
      innerBlocks: [],
      clientId: 'product-list',
      isValid: true,
    },
  ],

  stickyFooter: {
    blocks: [
      {
        name: 'shopify/collection-sort-filter',
        attributes: {
          isSearch: '{{checkIfTrueFalse(currentAction.params.showSearch)}}',
        },
      },
      {
        name: 'appmaker/floating-button',
        attributes: {
          __display:
            '{{checkIfTrueFalse(plugins.shopify.settings.enable_wishlist_floating_button)}}',
          iconName: 'heart',
          type: 'iconButton',
          visibilityStatus: true,
          appmakerAction: { action: 'OPEN_WISHLIST' },
        },
      },
    ],
  },
  _id: 'productList',
  uid: 'odRhv94hf4S52SysvvNoxPyRx682',
  // dataSource: ,
  toolBarItems: [ICONS.SEARCH, ICONS.WISHLIST, ICONS.CART],
};
export default pagesData;
