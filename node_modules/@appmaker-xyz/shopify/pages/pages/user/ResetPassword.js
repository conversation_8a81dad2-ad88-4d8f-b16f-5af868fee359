import { color, spacing } from '../../styles';
import { Platform } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';

const LoginPage = {
  type: 'normal',
  title: 'Reset Password',
  attributes: {
    // renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        removeClippedSubviews: false,
        insideKeyboardAvoidingView: Platform.OS === 'ios',
        keyboardVerticalOffset: 80,
        // renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          marginHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/remoteImage',
          attributes: {
            name: 'LOGIN_LOGO',
            style: {
              width: 150,
              height: 50,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.lg,
              marginTop: spacing.xl,
            },
          },
        },
        {
          name: 'appmaker/input',
          attributes: {
            label: 'Password',
            leftIcon: 'key',
            type: 'password',
            name: 'password',
            status: 'demiDark',
            autoCompleteType: 'password',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/input',
          attributes: {
            label: 'Confirm password',
            leftIcon: 'key',
            type: 'password',
            name: 'repeat_password',
            status: 'demiDark',
            autoCompleteType: 'password',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-reset-password',
          attributes: {
            appmakerAction: {
              action: 'RESET_PASSWORD_USING_LINK',
              params: '{{currentAction.params}}',
            },
            content: 'Reset password',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),

            __appmakerStylesClassName: 'registerButton',
          },
          contextValues: true,
        },
      ],
    },
  ],
};
export default LoginPage;
