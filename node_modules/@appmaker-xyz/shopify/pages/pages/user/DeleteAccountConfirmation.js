import { color, spacing } from '../../styles';

const DeleteAccountConfirmation = {
  type: 'normal',
  title: 'Delete Account',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 1,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/image',
          attributes: {
            advancedStyle: {
              width: 150,
              height: 150,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.xl,
            },
            uri: 'https://cdn-icons-png.flaticon.com/512/1632/1632658.png',
          },
        },
        {
          name: 'appmaker/text',
          attributes: {
            content:
              'Your account will be permanently deleted and you will lose access to your orders and other data.',
            category: 'h1Heading',
            style: {
              textAlign: 'center',
              marginBottom: 24,
            },
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'delete-account-confirmation-button-no',
          attributes: {
            appmakerAction: {
              action: 'GO_BACK',
            },
            content: 'Keep account and Go back',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: '#BBF7D0',
              marginBottom: 12,
            },
            fontColor: '#047857',
          },
          contextValues: true,
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'delete-account-confirmation-button-yes',
          attributes: {
            appmakerAction: {
              action: 'DELETE_ACCOUNT',
            },
            content: 'Delete account anyway',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: '#FECACA',
            },
            fontColor: '#B91C1C',
          },
          contextValues: true,
        },
      ],
    },
  ],
};
export default DeleteAccountConfirmation;
