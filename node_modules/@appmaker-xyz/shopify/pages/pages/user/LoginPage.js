import { color, spacing } from '../../styles';
import { appSettings } from '@appmaker-xyz/core';

const LoginPage = {
  type: 'normal',
  title: 'Login',
  attributes: {
    renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
          backgroundColor: color.white,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          paddingHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/remoteImage',
          attributes: {
            name: 'LOGIN_LOGO',
            style: {
              width: 200,
              height: 60,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.lg,
            },
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'username-email',
          attributes: {
            label: '<PERSON><PERSON><PERSON> or Email',
            leftIcon: 'user',
            name: 'username',
            status: 'demiDark',
            autoCompleteType: 'email',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'username-password',
          attributes: {
            label: 'Password',
            leftIcon: 'key',
            type: 'password',
            name: 'password',
            status: 'demiDark',
            autoCompleteType: 'password',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/blocksView',
          attributes: {
            renderType: 'normal',
            contentContainerStyle: {
              alignItems: 'flex-end',
              position: 'relative',
              top: -spacing.base,
            },
          },
          innerBlocks: [
            {
              name: 'appmaker/button',
              attributes: {
                appmakerAction: {
                  action: 'OPEN_FORGET_PASSWORD',
                },
                content: 'Forgot Password?',
                status: 'white',
                small: true,
              },
            },
          ],
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-login',
          attributes: {
            appmakerAction: {
              action: 'LOGIN_USER',
              params: '{{pageState}}',
            },
            content: 'Sign In',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),
            __appmakerStylesClassName: 'loginButton',
          },
          dependencies: {
            pageState: ['username', 'password'],
          },
          contextValues: true,
        },
        {
          name: 'appmaker/slot',
          clientId: 'login-page-social-login',
          attributes: {
            slotId: 'login-page-social-login',
          },
        },
        // {
        //   name: 'appmaker/GoogleLogin',
        //   attributes: {
        //     __display: displayHelper.shouldDisplayGoogleLogin,
        //     appmakerAction: {
        //       action: 'LOGIN_USER_VIA_GOOGLE',
        //     },
        //   },
        //   contextValues: true,
        // },
        // {
        //   name: 'appmaker/AppleLogin',
        //   attributes: {
        //     __display:
        //       displayHelper.shouldDisplayAppleLogin && Platform.OS === 'ios',
        //     appmakerAction: {
        //       action: 'LOGIN_USER_VIA_APPLE',
        //     },
        //   },
        //   contextValues: true,
        // },
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Do not have an account yet?',
            status: 'grey',
            style: {
              marginTop: spacing.xl,
              marginBottom: spacing.mini,
              alignSelf: 'center',
            },
          },
        },
        {
          name: 'appmaker/button',
          attributes: {
            testId: 'create-account',
            appmakerAction: {
              action: 'OPEN_REGISTER',
            },
            content: 'Create Account',
            outline: true,
            status: 'dark',
            baseSize: true,
            wholeContainerStyle: {
              borderColor: appSettings.getOption('register_button_color'),
            },
            fontColor: appSettings.getOption('register_button_color'),
            __appmakerStylesClassName: 'registerButton',
          },
        },
      ],
    },
  ],
};
export default LoginPage;
