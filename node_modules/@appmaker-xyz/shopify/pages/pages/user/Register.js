import { color, spacing } from '../../styles';
import { Platform } from 'react-native';
import { appSettings } from '@appmaker-xyz/core';

const LoginPage = {
  type: 'normal',
  title: 'Register Page',
  attributes: {
    // renderType: 'normal',
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/blocksView',
      attributes: {
        removeClippedSubviews: false,
        insideKeyboardAvoidingView: Platform.OS === 'ios',
        keyboardVerticalOffset: 80,
        // renderType: 'normal',
        rootContainerStyle: {
          flex: 5,
        },
        contentContainerStyle: {
          flex: 1,
          justifyContent: 'center',
          marginHorizontal: spacing.base,
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/remoteImage',
          attributes: {
            name: 'LOGIN_LOGO',
            style: {
              width: 150,
              height: 50,
              alignSelf: 'center',
              resizeMode: 'contain',
              marginBottom: spacing.lg,
              marginTop: spacing.xl,
            },
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'first-name',
          attributes: {
            __display:
              '{{!checkIfTrueFalse(plugins.shopify.settings.hide_name_field_in_register_page)}}',
            label: 'First name',
            leftIcon: 'user',
            name: 'first_name',
            status: 'demiDark',
            autoCompleteType: 'name',
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'last-name',
          attributes: {
            __display:
              '{{!checkIfTrueFalse(plugins.shopify.settings.hide_name_field_in_register_page)}}',
            label: 'Last name',
            leftIcon: 'user',
            name: 'last_name',
            status: 'demiDark',
            autoCompleteType: 'name',
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'email-field',
          attributes: {
            label: 'Enter Email',
            leftIcon: 'mail',
            name: 'email',
            status: 'demiDark',
            autoCompleteType: 'email',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'password-field',
          attributes: {
            label: 'Enter Password',
            leftIcon: 'key',
            type: 'password',
            name: 'password',
            status: 'demiDark',
            autoCompleteType: 'password',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/input',
          clientId: 'repeat-password-field',
          attributes: {
            label: 'Repeat Password',
            leftIcon: 'key',
            type: 'password',
            name: 'repeat_password',
            status: 'demiDark',
            autoCompleteType: 'password',
            __appmakerStylesClassName: 'inputCustom',
          },
        },
        {
          name: 'appmaker/checkbox-input',
          attributes: {
            testId: 'marketing-option',
            __display:
              '{{!checkIfTrueFalse(plugins.shopify.settings.hide_accept_marketing_in_register)}}',
            label: 'Agree to receive promotions and news by email',
            type: 'checkbox',
            activeColor: '#37AF9A',
            name: 'accepts_marketing',
            status: 'demiDark',
            defaultValue: true,
          },
        },
        {
          name: 'appmaker/checkbox-input',
          attributes: {
            testId: 'terms-condition',
            __display:
              '{{!checkIfTrueFalse(plugins.shopify.settings.hide_accept_terms_in_register)}}',
            label: 'Agree to Terms and Conditions',
            type: 'checkbox',
            activeColor: '#37AF9A',
            name: 'agree_terms',
            status: 'demiDark',
          },
        },
        {
          name: 'appmaker/ActionButton',
          clientId: 'submit-register',
          attributes: {
            appmakerAction: {
              action: 'REGISTER_USER',
            },
            content: 'Create Account',
            baseSize: true,
            wholeContainerStyle: {
              backgroundColor: appSettings.getOption('primary_button_color'),
            },
            fontColor: appSettings.getOption('primary_button_text_color'),

            __appmakerStylesClassName: 'registerButton',
          },
          contextValues: true,
        },
        {
          name: 'appmaker/text',
          attributes: {
            content: 'Already have an account?',
            status: 'grey',
            style: {
              marginTop: spacing.xl,
              marginBottom: spacing.mini,
              alignSelf: 'center',
            },
          },
        },
        {
          name: 'appmaker/button',
          attributes: {
            testId: 'login-button',
            appmakerAction: {
              action: 'OPEN_LOGIN_PAGE',
            },
            content: 'Sign In',
            outline: true,
            status: 'dark',
            baseSize: true,
            // wholeContainerStyle: {
            //   borderColor: configColors.SECONDARY_BUTTON_COLOR,
            // },
            // fontColor: configColors.SECONDARY_BUTTON_COLOR,
            __appmakerStylesClassName: 'loginButton',
          },
        },
      ],
    },
  ],
};
export default LoginPage;
