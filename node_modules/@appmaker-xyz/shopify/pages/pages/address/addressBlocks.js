import { getShipsToCountries } from '../../../data/countries';
import { spacing } from '../../styles';

const blocks = [
  {
    name: 'appmaker/layout',
    innerBlocks: [
      {
        name: 'appmaker/shopify-address-listener',
        attributes: {},
      },
      {
        name: 'appmaker/layout',
        attributes: {
          style: {
            marginVertical: spacing.md,
            borderBottomWidth: 1,
            borderBottomColor: '#e6e6e6',
            paddingHorizontal: spacing.base,
          },
        },
        innerBlocks: [
          {
            name: 'appmaker/select',
            attributes: {
              label: '{{pageState.addressLabels.country}}',
              status: 'grey',
              name: 'country',
              setValueIfOnlyOne: true,
              selectListFn: getShipsToCountries,
              avilableCountries: '{{getShipsToCountries()}}',
              defaultValue:
                '{{ !blockItem.node ? "" : blockItem.node.country}}',
            },
          },
          {
            name: 'appmaker/select',
            attributes: {
              label: '{{pageState.addressLabels.province}}',
              status: 'grey',
              name: 'province',
              pageState_address_format: '{{pageState.address_format }}',
              __display:
                '{{ pageState.address_format.match(/provice/).length > 0 }}',
              setValueIfOnlyOne: true,
              selectList: '{{pageState.provinceList}}',
              defaultValue:
                '{{ !blockItem.node ? "" : blockItem.node.province}}',
            },
          },
          {
            name: 'appmaker/text',
            attributes: {
              content: 'Contact Information',
              status: 'demiDark',
              category: 'actionTitle',
              style: {
                marginBottom: spacing.small,
              },
            },
          },
          {
            name: 'appmaker/floating-label-input',
            attributes: {
              label: 'Email Id',
              defaultValue: '{{ !blockItem.node ? "" : blockItem.node.email}}',
              name: 'email',
              status: 'grey',
              nextFieldName: 'firstName',
            },
          },
        ],
      },
    ],
  },
  {
    name: 'appmaker/layout',
    attributes: {
      style: {
        paddingHorizontal: spacing.base,
      },
    },
    innerBlocks: [
      {
        name: 'appmaker/text',
        attributes: {
          content: 'Shipping Address',
          status: 'demiDark',
          category: 'actionTitle',
          style: {
            marginBottom: spacing.small,
          },
        },
      },
      {
        name: 'appmaker/form-input-hidden',
        clientId: 'address-id',
        attributes: {
          value: '{{!blockItem.node ? "" : blockItem.node.id}}',
          name: 'id',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          // label: 'First Name',
          label: '{{pageState.addressLabels.firstName}}',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.firstName}}',
          name: 'firstName',
          status: 'grey',
          nextFieldName: 'lastName',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: '{{pageState.addressLabels.lastName}}',
          name: 'lastName',
          status: 'grey',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.lastName}}',
          nextFieldName: 'address1',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: '{{pageState.addressLabels.address1}}',
          name: 'address1',
          status: 'grey',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.address1}}',
          nextFieldName: 'address2',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: '{{pageState.addressLabels.address2}}',
          name: 'address2',
          status: 'grey',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.address2}}',
          nextFieldName: 'city',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label:
            '{{pageState.addressLabels.address2 ? "City" : "Town / City"}}',
          name: 'city',
          status: 'grey',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.city}}',
          nextFieldName: 'zip',
        },
      },
      // {
      //   name: 'appmaker/floating-label-input',
      //   attributes: {
      //     label: 'country',
      //     name: 'country',
      //     status: 'grey',
      //     defaultValue: '{{ !blockItem.node ? "" : blockItem.node.country}}',
      //   },
      // },
      // {
      //   name: 'appmaker/select',
      //   attributes: {
      //     label: 'Country',
      //     status: 'grey',
      //   },
      // },
      // {
      //   name: 'appmaker/floating-label-input',

      //   attributes: {
      //     label: 'Province',
      //     name: 'province',
      //     status: 'grey',
      //     defaultValue: '{{ !blockItem.node ? "" : blockItem.node.province}}',
      //   },
      // },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: 'PIN code',
          name: 'zip',
          status: 'grey',
          type: 'number',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.zip}}',
          nextFieldName: 'phone',
        },
      },
      {
        name: 'appmaker/floating-label-input',
        attributes: {
          label: 'Mobile Number',
          name: 'phone',
          status: 'grey',
          caption: 'In case we need to contact you about your order',
          type: 'number',
          defaultValue: '{{ !blockItem.node ? "" : blockItem.node.phone}}',
        },
      },
    ],
  },
];

export { blocks };
