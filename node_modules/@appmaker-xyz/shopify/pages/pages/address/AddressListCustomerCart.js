const pickAddress = {
  type: 'normal',
  title: 'Shipping Address',
  attributes: {
    showLoadingTillData: true,
    renderType: 'normal',
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: '#fff',
    },
    contentContainerStyle: {
      flex: 1,
    },
  },
  blocks: [
    {
      name: 'appmaker/layout',
      attributes: {
        style: {
          borderBottomWidth: 4,
          borderBottomColor: '#e6e6e6',
          borderTopWidth: 4,
          borderTopColor: '#e6e6e6',
        },
      },
      innerBlocks: [
        {
          name: 'appmaker/button',
          attributes: {
            content: '+ Add new address',
            small: true,
            outline: true,
            status: 'primary',
            wholeContainerStyle: {
              borderRadius: 0,
              borderWidth: 0,
              paddingVertical: 12,
            },
            appmakerAction: {
              action: 'OPEN_SHIPPING_ADDRESS_CREATE',
            },
          },
        },
      ],
    },
    {
      name: 'appmaker/address-chooser',
      attributes: {
        defaultAddressId: '{{pageState.blockData.defaultAddress.id}}',
        id: '{{blockItem.node.id}}',
        firstName: '{{blockItem.node.firstName}}',
        lastName: '{{blockItem.node.lastName}}',
        address1: '{{blockItem.node.address1}}',
        address2: '{{blockItem.node.address2}}',
        city: '{{blockItem.node.city}}',
        province: '{{blockItem.node.province}}',
        company: '{{blockItem.node.company}}',
        country: '{{blockItem.node.country}}',
        phone: '{{blockItem.node.phone}}',
        zip: '{{blockItem.node.zip}}',
        formatted: '{{blockItem.node.formatted}}',
        loading: false,
        appmakerAction: {
          action: 'SET_SHIPPING_ADDRESS',
          params: {
            address: '{{blockItem.node}}',
          },
        },
        appmakerEditAction: {
          action: 'EDIT_SHIPPING_ADDRESS_CUSTOMER',
          params: {
            pageData: '{{blockItem}}',
          },
        },
        dataSource: {
          responseType: 'replace',
          repeatable: 'Yes',
          attributes: {
            params: '{{blockData.addresses.edges}}',
          },
          source: 'variables',
          repeatItem: 'DataVariable',
        },
      },
    },
  ],
  dataSource: {
    source: 'shopify',
    attributes: {
      reloadOnFocus: true,
      mapping: {
        items: 'data.data.customer',
      },
      methodName: 'customerAddressList',
      params: '{{appStorageStateApi.user}}',
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  },
};
export default pickAddress;
