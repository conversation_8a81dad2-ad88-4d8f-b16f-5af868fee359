import { color, spacing } from '../../styles';
import { blocks } from './addressBlocks';

const shippingAddressCreateCustomer = {
  type: 'normal',
  title: 'Edit Address',
  attributes: {
    renderInScroll: true,
    renderType: 'normal',
    showLoadingTillData: true,
    insideSafeAreaView: true,
    rootContainerStyle: {
      flex: 1,
      backgroundColor: color.white,
    },
    contentContainerStyle: {
      flex: 1,
      marginHorizontal: spacing.md,
    },
  },
  blocks: blocks,
  fixedFooter: {
    clientId: 'checkout-sumbit',
    name: 'appmaker/button',
    attributes: {
      content: 'Save Address',
      wholeContainerStyle: {
        borderRadius: 0,
      },
      appmakerAction: {
        action: 'UPDATE_ADDRESS',
      },
    },
  },
};
export default shippingAddressCreateCustomer;
