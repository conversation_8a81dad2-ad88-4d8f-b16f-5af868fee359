import { spacing, color } from '../styles';
const pageState = {
  query: '',
  searchLoading: false,
  suggestions: [
    {
      label: 'Suggestion 1',
      query: 'Suggestion 1',
    },
  ],
  products: [],
};
const searchPage = {
  title: 'Search',
  attributes: {
    rootContainerStyle: {
      flex: 1,
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: color.light,
    },
  },
  blocks: [
    {
      name: 'shopify/search-bar',
      attributes: {
        label: 'Search Product',
        topBarView: false,
        debounceInterval: 500,
        name: 'searchKey',
        appmakerAction: {
          action: 'LIST_PRODUCT',
          params: {
            replacePage: true,
            title: 'Result',
          },
        },
      },
    },
    // if search<PERSON>ey is empty, show recent search
    {
      name: 'shopify/empty-search-query',
      innerBlocks: [
        {
          name: 'shopify/recent-search',
          attributes: {},
        },
        {
          name: 'shopify/trending-search-keywords',
          attributes: {},
        },
      ],
    },
    {
      name: 'shopify/search-result',
      innerBlocks: [
        {
          name: 'shopify/search-suggestion-keywords',
        },
        {
          name: 'shopify/search-suggestion-products',
        },
      ],
    },
    // {
    //   name: 'shopify/no-search-result',
    //   innerBlocks: [
    //     {
    //       name: 'shopify/search-suggestion-no-result',
    //     },
    //     {
    //       name: 'shopify/search-collection',
    //     },
    //   ],
    // },
  ],
};
export default searchPage;
