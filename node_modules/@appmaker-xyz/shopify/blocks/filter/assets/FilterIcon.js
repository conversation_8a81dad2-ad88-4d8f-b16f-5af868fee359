import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const FilterIcon = (props) => (
  <Svg
    width={16}
    height={16}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <Path
      d="M11.217 7.277a2.286 2.286 0 0 0 2.223-1.714h1.989a.571.571 0 0 0 0-1.143H13.44a2.285 2.285 0 0 0-4.446 0H.571a.571.571 0 1 0 0 1.143h8.423a2.286 2.286 0 0 0 2.223 1.714Zm-1.143-2.286a1.143 1.143 0 1 1 1.143 1.143 1.143 1.143 0 0 1-1.16-1.143h.017ZM4.286 12.99a2.285 2.285 0 0 0 2.223-1.714h8.92a.571.571 0 0 0 0-1.143h-8.92a2.286 2.286 0 0 0-4.446 0H.57a.571.571 0 1 0 0 1.143h1.492a2.286 2.286 0 0 0 2.223 1.715Zm-1.143-2.285a1.143 1.143 0 1 1 1.143 1.143 1.143 1.143 0 0 1-1.16-1.143h.017Z"
      fill={props.color || '#fff'}
    />
  </Svg>
);

export default FilterIcon;
