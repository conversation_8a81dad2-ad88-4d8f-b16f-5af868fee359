import React from 'react';
import { CheckBox } from '@appmaker-xyz/ui';
import { useSelectedFilterItem } from '../../../hooks/filter/userCollectionFilter';
import { FlatList, StyleSheet, Text, Image, View } from 'react-native';

function ItemLabel({ item, count, isUpperCase, label }) {
  if (item?.displayType === 'IMAGE' && item?.image) {
    return (
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: item.image }}
          style={styles.colorBox}
          resizeMode="cover"
        />
        <Text style={styles.activeCountText}>({count})</Text>
      </View>
    );
  } else if (item?.displayType === 'COLOR_CODE' && item?.color_code) {
    return (
      <View style={styles.imageContainer}>
        <View style={[styles.colorBox, { backgroundColor: item.color_code }]} />
        <Text style={styles.activeCountText}>({count})</Text>
      </View>
    );
  } else {
    return (
      <View style={styles.imageContainer}>
        <Text
          style={
            isUpperCase ? styles.checkBoxItemCaptialize : styles.checkBoxItem
          }>
          {label}
        </Text>
        <Text style={styles.activeCountText}>({count})</Text>
      </View>
    );
  }
}

function CheckBoxItem({
  label,
  count,
  selectFilter,
  removeFilter,
  filterKey,
  item,
  parentItem,
}) {
  const selectedItem = useSelectedFilterItem({
    filterKey,
    filterValueId: item.id,
  });
  const isUpperCase = parentItem?.upper_case || false;

  return (
    <CheckBox
      activeColor={'#000'}
      small={true}
      label={
        <ItemLabel
          item={item}
          count={count}
          isUpperCase={isUpperCase}
          label={label}
        />
      }
      value={selectedItem?.id === item.id}
      onValueChange={(status) => {
        if (status) {
          selectFilter(filterKey, item.id, item);
        } else {
          removeFilter(filterKey, item.id, item);
        }
      }}
    />
  );
}

function FilterColorSwatch({ item, selectFilter, removeFilter }) {
  const { values } = item;

  function renderItem({ item: filterItem }) {
    return (
      <CheckBoxItem
        label={filterItem.label}
        count={filterItem.count}
        selectFilter={selectFilter}
        removeFilter={removeFilter}
        filterKey={item.id}
        item={filterItem}
        parentItem={item}
      />
    );
  }
  return (
    <FlatList
      data={values}
      renderItem={renderItem}
      contentContainerStyle={styles.filterContentViewContainer}
      keyExtractor={(item, index) => index.toString()}
    />
  );
}

const styles = StyleSheet.create({
  activeCountText: {
    color: '#64748B',
    padding: 2,
    borderRadius: 4,
    marginLeft: 6,
    fontSize: 12,
  },
  filterContentViewContainer: {
    padding: 10,
  },
  checkBoxItemCaptialize: {
    textTransform: 'capitalize',
  },
  checkBoxItem: {},
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  colorBox: {
    width: 50,
    height: 22,
    borderRadius: 25,
    overflow: 'hidden',
  },
});

export default FilterColorSwatch;
