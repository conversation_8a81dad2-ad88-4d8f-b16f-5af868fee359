type Compare = {
  left_operand: string | number | boolean;
  operator: Operator;
  right_operand: string | number | boolean;
};

enum Operator {
  EQ = 'eq',
  NEQ = 'neq',
  LT = 'lt',
  LTEQ = 'lteq',
  GT = 'gt',
  GTEQ = 'gteq',
}

function compare(
  left_operand: string | number | boolean,
  operator: Operator,
  right_operand: string | number | boolean,
): boolean {
  const operations: { [key in Operator]: (a: any, b: any) => boolean } = {
    [Operator.EQ]: (a, b) => a == b,
    [Operator.NEQ]: (a, b) => a !== b,
    [Operator.LT]: (a, b) => a < b,
    [Operator.LTEQ]: (a, b) => a <= b,
    [Operator.GT]: (a, b) => a > b,
    [Operator.GTEQ]: (a, b) => a >= b,
  };

  const operation = operations[operator];
  return operation ? operation(left_operand, right_operand) : false;
}

export { compare };
export type { Compare, Operator };
