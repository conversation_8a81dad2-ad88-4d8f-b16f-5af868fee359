import React, { useEffect } from 'react';
import { useCondition } from './useCondition';

export default function ConditionBlockItem(props) {
  const {
    attributes,
    BlockItem,
    onAction,
    currentAction,
    BlocksView,
    innerBlocks,
  } = props;

  const { isBlockVisible } = useCondition(props);

  if (isBlockVisible) {
    return (
      <BlocksView
        onAction={onAction}
        inAppPage={{
          blocks: innerBlocks,
          attributes: {
            renderType: 'normal',
          },
        }}
      />
    );
  } else {
    return null;
  }
}
