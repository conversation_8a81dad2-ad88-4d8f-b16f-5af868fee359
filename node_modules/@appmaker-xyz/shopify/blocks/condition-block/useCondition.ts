import { useMemo, useCallback } from 'react';
import { useUser, usePluginStore } from '@appmaker-xyz/core';
import { useCurrentUser } from '../../hooks/all';
import { isEmpty } from 'lodash';
import { useColorScheme } from 'react-native';
import { compare, Operator } from './utils';

type ComparisonCondition<T> = {
  [key: string]: {
    namespace: string;
    key: string;
    value: T;
    compare: Operator;
    title: string;
  };
};

interface PartialAttributes {
  condition_type: string;
  raw_condition?: string;
  user_tags?: string;
  has_all_tags?: boolean;
  metafields?: ComparisonCondition<string>;
}
interface PartialUseConditionProps {
  attributes: PartialAttributes;
}

function isTrue(value: any): boolean {
  return value === true || value === 'true';
}

function parseValue(value: any): number | string {
  const parsed = parseFloat(value);
  return isNaN(parsed) ? value : parsed;
}

function evalMetafields(
  metafields?: ComparisonCondition<string>,
  current_user_metafields?:
    | ({
        namespace: string;
        key: string;
        value: string;
      } | null)[]
    | undefined,
) {
  if (!metafields) return false;
  const parsedMetafields = Object.keys(metafields).map((index) => {
    const metafield = metafields[index];
    const { namespace, key, value, compare } = metafield;
    return {
      namespace,
      key,
      value,
      compare,
    };
  });

  return parsedMetafields.every(
    ({ namespace, key, value, compare: comparator }) => {
      const currentMetafield = current_user_metafields?.find(
        (metafield) =>
          metafield?.namespace === namespace && metafield?.key === key,
      );

      if (!currentMetafield) return false;

      const currentValue = parseValue(value);
      const currentMetafieldValue = parseValue(currentMetafield.value);

      return compare(currentMetafieldValue, comparator, currentValue);
    },
  );
}

export function useCondition(props: PartialUseConditionProps): {
  isBlockVisible: boolean;
  condition_type: string;
} {
  const { attributes } = props;
  const { condition_type, raw_condition, user_tags, has_all_tags, metafields } =
    attributes;

  const requestMetafields = useMemo(() => {
    if (!metafields) return [];
    return Object.values(metafields).map(
      ({ namespace, key }: { namespace: string; key: string }) => ({
        namespace,
        key,
      }),
    );
  }, [metafields]);

  const { data } = useCurrentUser({ metafields: requestMetafields });

  const { isLoggedin } = useUser();

  const current_user_tags = data?.customer?.tags;

  const formatted_user_tags = useMemo(
    () =>
      user_tags
        ?.split(',')
        .map((tag: string) => tag.trim())
        .filter(Boolean) || [],
    [user_tags],
  );

  const colorScheme = useColorScheme();

  const isSystemThemeEnabled = usePluginStore(
    (state) =>
      state?.plugins['app-branding']?.settings?.isSystemThemeSwitchEnabled,
  );

  const checkTags = useCallback(
    (
      tags: string[],
      current_user_tags: string[] | undefined,
      has_all_tags?: boolean,
      contains?: boolean,
    ): boolean => {
      const hasAllTags = isTrue(has_all_tags);
      const tagSet = new Set(current_user_tags);
      const method = hasAllTags ? 'every' : 'some';
      return contains
        ? tags[method]((tag) => tagSet.has(tag))
        : tags[method]((tag) => !tagSet.has(tag));
    },
    [],
  );

  const evaluateCondition = useCallback(() => {
    switch (condition_type) {
      case 'user_logged_in':
        return isLoggedin;
      case 'guest_user':
        return !isLoggedin;
      case 'use_user_tags_contain':
        if (isEmpty(current_user_tags)) return false;
        return checkTags(
          formatted_user_tags,
          current_user_tags,
          has_all_tags,
          true,
        );
      case 'user_tags_not_contain':
        if (isEmpty(current_user_tags)) return true;
        return checkTags(
          formatted_user_tags,
          current_user_tags,
          has_all_tags,
          false,
        );
      case 'use_metafields':
        return evalMetafields(metafields, data?.customer?.metafields);
      case 'dark_mode':
        return colorScheme === 'dark' && isSystemThemeEnabled;
      case 'light_mode':
        return colorScheme === 'light' && isSystemThemeEnabled;
      default:
        return isEmpty(raw_condition) ? false : isTrue(raw_condition);
    }
  }, [
    condition_type,
    isLoggedin,
    current_user_tags,
    formatted_user_tags,
    has_all_tags,
    metafields,
    data?.customer?.metafields,
    colorScheme,
    isSystemThemeEnabled,
    raw_condition,
    checkTags,
  ]);

  const isBlockVisible = !!evaluateCondition();

  return { isBlockVisible, condition_type };
}
