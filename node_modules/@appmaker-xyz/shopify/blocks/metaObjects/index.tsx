import { View, Text, ActivityIndicator } from 'react-native';
import React from 'react';
import { useMetaObjectListQuery } from '../../hooks/metaObject/useMetaObjectListQuery';
import { FlashList } from '@shopify/flash-list';
import { BlockItemProps } from '@appmaker-xyz/react-native';

export default function MetaObjectList({
  BlockItemRender,
  attributes,
  BlocksView,
  onAction,
  innerBlocks,
  BlockItem,
  currentAction,
}: BlockItemProps) {
  const {
    type,
    itemBlockName = 'shopify/meta-object-item',
    numColumns = 1,
    estimatedItemSize = 381,
    itemAttributes = {},
    itemInnerBlocks = [],
  } = attributes;
  const { items, isFetchingNextPage, fetchNextPage, isLoading } =
    useMetaObjectListQuery({
      type: type,
    });
  function renderItem({ item }) {
    return (
      <BlockItemRender
        blockData={item}
        onAction={onAction}
        block={{
          name: itemBlockName,
          innerBlocks: itemInnerBlocks,
          attributes: {
            gridViewListing: true,
            numColumns: numColumns,
            ...itemAttributes,
          },
        }}
      />
    );
  }
  return (
    <View
      style={{
        flex: 1,
      }}>
      {isLoading ? (
        <ActivityIndicator size="small" color={'#000'} />
      ) : (
        <FlashList
          data={items?.list || []}
          numColumns={numColumns}
          estimatedItemSize={estimatedItemSize}
          keyExtractor={(item, index) => index.toString()}
          ListFooterComponent={() =>
            isFetchingNextPage ? (
              <ActivityIndicator size="small" color={'#000'} />
            ) : null
          }
          onEndReachedThreshold={4}
          onEndReached={fetchNextPage}
          renderItem={renderItem}
        />
      )}
    </View>
  );
}
