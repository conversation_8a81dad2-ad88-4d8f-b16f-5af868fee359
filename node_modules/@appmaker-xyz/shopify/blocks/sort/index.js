import React from 'react';
import {
  ShopifyFilterProvider,
  createFilterStore,
} from '../../hooks/filter/filterStore';
import SortButton from './components/SortButton';
import SortModal from './SortModal';

// export default function Sort(props) {
//   return (
//     <>
//       {/* <ShopifyFilterProvider
//         createStore={createFilterStore({
//           selectedFillters: {},
//         })}> */}
//       {/* <SortButton /> */}
//       <SortModal
//       //   avilableFilters={props.blockData}
//       />
//       {/* </ShopifyFilterProvider> */}
//     </>
//   );
// }
