import React from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  useIsAnySortSelected,
  useSortModal,
} from '../../../hooks/filter/userCollectionFilter';
import { ThemeText } from '@appmaker-xyz/ui';
import SortIcon from '../assets/SortIcon';
import { testProps, getExtensionConfig } from '@appmaker-xyz/core';

export function SortButton({ attributes: { customStyles = {} } = {} }) {
  const {
    activeColor,
    bgColor,
    textColor,
    outline,
    noIcon,
    small,
    wholeContainerStyle,
  } = customStyles;

  const extensionStyles = getExtensionConfig?.(
    'shopify',
    'sortButtonGroup',
    {},
  );

  const { openSortModal } = useSortModal();
  const isSortApplied = useIsAnySortSelected();

  const containerStyles = [
    styles.sortButton,
    extensionStyles?.sortButtonContainer,
  ];
  const activeContainerStyles = [styles.activeContainer];

  if (bgColor) {
    containerStyles.push({ backgroundColor: bgColor });
  }
  if (outline) {
    containerStyles.push({ borderWidth: 1, borderColor: outline });
  }
  if (noIcon) {
    activeContainerStyles.push({ marginLeft: 0 });
  }
  if (small) {
    containerStyles.push({ paddingVertical: 8 });
  }
  if (wholeContainerStyle) {
    containerStyles.push(wholeContainerStyle);
  }

  return (
    <Pressable
      {...testProps(`sort-button`)}
      onPress={openSortModal}
      style={containerStyles}>
      {noIcon ? null : (
        <SortIcon
          color={extensionStyles?.sortButtonIconColor || textColor}
          style={extensionStyles?.sortIcon}
        />
      )}
      <View style={activeContainerStyles}>
        {isSortApplied ? (
          <View
            style={[
              styles.activeDot,
              {
                backgroundColor:
                  extensionStyles?.activeDotColor || activeColor || '#ff0000',
              },
              extensionStyles?.sortActiveDot,
            ]}
          />
        ) : null}
        <ThemeText
          style={[styles.sortButtonText, extensionStyles?.sortText]}
          color={textColor ? textColor : '#fff'}>
          Sort
        </ThemeText>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  sortButton: {
    flex: 1,
    backgroundColor: '#000',
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sortButtonText: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 20,
  },
  activeContainer: {
    position: 'relative',
    marginLeft: 6,
  },
  activeDot: {
    position: 'absolute',
    zIndex: 1,
    top: 2,
    right: -16,
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
});

export default SortButton;
