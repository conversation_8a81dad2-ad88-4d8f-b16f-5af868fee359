import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import { ThemeText } from '@appmaker-xyz/ui';
export default function StarRating(props) {
  const { onChangeRating } = props;
  const [rating, setRating] = React.useState(0);
  const [tempRating, setTempRating] = React.useState(0);

  const starArray = [1, 2, 3, 4, 5];

  const handleRating = (rating) => {
    onChangeRating && onChangeRating(rating);
    setRating(rating);
    setTempRating(rating);
  };

  const star = starArray.map((_, i) => {
    const ratingValue = i + 1;

    return (
      <Icon
        key={ratingValue}
        name={ratingValue <= (tempRating || rating) ? 'star' : 'staro'}
        size={32}
        color={ratingValue <= (tempRating || rating) ? '#ffc107' : '#e4e5e9'}
        onPress={() => handleRating(ratingValue)}
        style={styles.starIcon}
      />
    );
  });

  return (
    <View style={styles.starContainer}>
      <ThemeText>Your Rating</ThemeText>
      <View style={styles.star}>{star}</View>
    </View>
  );
}

const styles = StyleSheet.create({
  starContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  star: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 4,
  },
  starIcon: {
    marginHorizontal: 4,
  },
});
