import React from 'react';
import { View, StyleSheet, Pressable, TextInput } from 'react-native';
import StarRating from './components/StarRating';
import { useTranslation } from 'react-i18next';
import { ThemeText } from '@appmaker-xyz/ui';
import { customStyles } from '@appmaker-xyz/shopify/styles/custom';

export default function AddReview(props) {
  const { t } = useTranslation();
  const [show, setShow] = React.useState(false);
  const { onChangeInput, onSubmit, onCancel } = props;
  const onValueChange = (key, value) => {
    onChangeInput && onChangeInput(key, value);
  };
  return (
    <View>
      {/* <Pressable onPress={() => setShow(true)}>
        <Text>Write review</Text>
      </Pressable> */}
      {/* <Modal isVisible={show} style={styles.modal}> */}
      <View style={styles.modalContainer}>
        <View style={styles.topBar}>
          <ThemeText style={styles.modalHeadText}>Write review</ThemeText>
        </View>
        <View style={styles.modalContentContainer}>
          <TextInput
            placeholder={t('Name')}
            style={styles.input}
            onChangeText={(value) => {
              onValueChange('name', value);
            }}
          />
          <TextInput
            placeholder={t('Email')}
            style={styles.input}
            onChangeText={(value) => {
              onValueChange('email', value);
            }}
          />
          <StarRating
            onChangeRating={(value) => {
              onValueChange('rating', value);
            }}
          />
          <TextInput
            placeholder={t('Title')}
            style={styles.input}
            onChangeText={(value) => {
              onValueChange('title', value);
            }}
          />
          <TextInput
            placeholder={t('Review')}
            style={styles.textArea}
            editable={true}
            multiline={true}
            numberOfLines={6}
            onChangeText={(value) => {
              onValueChange('review', value);
            }}
          />
          <View style={styles.buttons}>
            <Pressable style={styles.button1} onPress={onCancel}>
              <ThemeText style={styles.buttonText1}>Cancel</ThemeText>
            </Pressable>
            <Pressable style={styles.button2} onPress={onSubmit}>
              <ThemeText style={styles.buttonText2}>Submit</ThemeText>
            </Pressable>
          </View>
        </View>
      </View>
      {/* </Modal> */}
    </View>
  );
}

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    overflow: 'hidden',
  },
  topBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  modalHeadText: {
    ...customStyles.styles.fontFamily.regular,
    color: '#1B1B1B',
    flexGrow: 1,
    fontSize: 16,
  },
  back: {
    padding: 6,
    borderRadius: 18,
  },
  modalContentContainer: {
    padding: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e2e2',
    borderRadius: 4,
    padding: 8,
    marginBottom: 16,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e2e2e2',
    borderRadius: 4,
    padding: 8,
    marginBottom: 16,
    textAlignVertical: 'top',
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button1: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e2e2e2',
    flex: 1,
    marginRight: 8,
  },
  buttonText1: {
    textAlign: 'center',
    color: '#1B1B1B',
    fontSize: 16,
  },
  button2: {
    backgroundColor: '#000',
    paddingVertical: 12,
    borderRadius: 4,
    flex: 2,
    marginLeft: 8,
  },
  buttonText2: {
    color: '#fff',
    textAlign: 'center',
    fontSize: 16,
  },
});
