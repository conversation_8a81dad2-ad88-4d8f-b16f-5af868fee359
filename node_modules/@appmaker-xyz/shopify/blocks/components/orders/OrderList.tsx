import React from 'react';
import { StyleSheet, ActivityIndicator } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { Layout } from '@appmaker-xyz/ui';
import { useOrders } from '../../../hooks/order/useOrders';
type OrderListProp = {
  BlockItemRender: React.FC<any>;
  onAction: () => any;
  loadingComponent?: () => React.ReactElement;
  numColumns?: number;
  beforeOrderRender?: (items: any[]) => any[];
  ordersLimit?: number;
  attributes?: any;
};

const OrderList: React.FC<OrderListProp> = (props) => {
  const {
    BlockItemRender,
    onAction,
    loadingComponent,
    numColumns,
    beforeOrderRender,
  } = props;
  const { isLoading, orderList, isFetching, hasNextPage, fetchNextPage } =
    useOrders({
      ...(props?.ordersLimit && { limit: props?.ordersLimit }),
    });
  const loadNext = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };
  const OrderBlockName = 'shopify/order-card';
  let orderListData =
    typeof beforeOrderRender === 'function'
      ? beforeOrderRender(orderList)
      : orderList;

  if (orderListData?.length === 0) {
    return (
      <BlockItemRender
        onAction={onAction}
        BlockItemRender={BlockItemRender}
        block={{
          name: 'appmaker/error-block',
          attributes: {
            empty: true,
            emptyText: 'No Items',
          },
        }}
      />
    );
  }

  return (
    <Layout
      loading={isLoading}
      loadingLayout={'normal'}
      loadingComponent={loadingComponent}
      style={styles?.container}>
      <FlashList
        key={`order-list-`}
        data={orderListData}
        numColumns={numColumns || 1}
        bounces={false}
        contentContainerStyle={styles.flatListContainer}
        onEndReached={loadNext}
        estimatedItemSize={381}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() =>
          !isLoading && isFetching ? (
            <ActivityIndicator size="small" color="#000000" />
          ) : null
        }
        renderItem={({ item, extraData, index }) => (
          <BlockItemRender
            extraData={extraData}
            blockData={item}
            onAction={onAction}
            blocksViewItemIndex={index}
            block={{
              name: OrderBlockName,
              attributes: {
                ...props?.attributes,
                orderId: item?.node?.name,
                __experimentalDisableListItemParser: true,
                gridViewListing: true,
              },
            }}
          />
        )}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatListContainer: {
    paddingHorizontal: 5,
  },
});

export default OrderList;
