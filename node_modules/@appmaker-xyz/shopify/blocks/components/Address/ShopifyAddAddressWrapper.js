import React from 'react';
import {
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAddAddress } from '../../../hooks/cart/useAddress';
import { Input, DropSelect, Layout, Button } from '@appmaker-xyz/ui';
const ShopifyAddAddressWrapper = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  ...props
}) => {
  const {
    addNewAddress,
    updateForm,
    formData,
    statesList,
    countriesListFn,
    isAddAddressLoading,
    isEditAddress,
  } = useAddAddress({
    onAction,
    address: blockData?.address,
  });
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS == 'ios' ? 'padding' : 'height'}>
      <Layout>
        <ScrollView contentContainerStyle={styles.inputsContainer}>
          <DropSelect
            attributes={{
              label: 'Country',
              setValueIfOnlyOne: true,
              selectListFn: countriesListFn,
              value: formData.country,
              onChange: (value) => {
                updateForm('country', value.value);
              },
            }}
          />
          <Layout style={styles.nameRow}>
            <Input
              label="First Name"
              placeholder="Enter First Name"
              style={styles.fName}
              returnKeyType="next"
              value={formData.firstName}
              onChange={(value) => {
                updateForm('firstName', value);
              }}
            />
            <Input
              label="Last Name"
              placeholder="Enter Last Name"
              style={styles.lName}
              returnKeyType="next"
              value={formData.lastName}
              onChange={(value) => {
                updateForm('lastName', value);
              }}
            />
          </Layout>
          <Input
            label="Address"
            placeholder="Enter Address"
            returnKeyType="next"
            value={formData.address1}
            onChange={(value) => {
              updateForm('address1', value);
            }}
          />
          {/* <AppTouchable style={styles.addNewLineButton}>
            <Icon
              name="plus"
              size={12}
              color="#3280FE"
              style={styles.plusIcon}
            />
            <ThemeText color="#3280FE">Street Address 2 (Optional)</ThemeText>
          </AppTouchable> */}
          <Input
            label="Address"
            placeholder="Apartment, suite, etc. (optional)"
            returnKeyType="next"
            value={formData.address2}
            onChange={(value) => {
              updateForm('address2', value);
            }}
          />
          <Input
            label="Email"
            placeholder="Enter Email"
            returnKeyType="next"
            autoComplete="email"
            keyboardType="email-address"
            value={formData.email}
            onChange={(value) => {
              updateForm('email', value);
            }}
          />
          <Input
            label="Phone"
            placeholder="Enter Phone"
            returnKeyType="next"
            autoComplete="tel"
            keyboardType="phone-pad"
            value={formData.phone}
            onChange={(value) => {
              updateForm('phone', value);
            }}
          />
          <Input
            label="City"
            placeholder="Enter City"
            value={formData.city}
            onChange={(value) => {
              updateForm('city', value);
            }}
          />

          {/* <DropSelect attributes={{ label: 'City' }} /> */}
          <DropSelect
            attributes={{
              label: 'State | province',
              setValueIfOnlyOne: true,
              selectList: statesList,
              value: formData.province,
              onChange: (value) => {
                updateForm('province', value.value);
              },
            }}
          />

          <Input
            label="Pin Code"
            placeholder="Enter Pin Code"
            autoComplete="postal-code"
            keyboardType="number-pad"
            returnKeyType="done"
            value={formData.zip}
            onChange={(value) => {
              updateForm('zip', value);
            }}
          />
          {/* <DropSelect attributes={{ label: 'City' }} /> */}
          <Button
            title={isEditAddress ? 'Update Address' : 'Add Address'}
            isLoading={isAddAddressLoading}
            containerStyles={styles.button}
            onPress={() => {
              addNewAddress(formData);
            }}
          />
        </ScrollView>
      </Layout>
    </KeyboardAvoidingView>
  );
};
const styles = StyleSheet.create({
  container: { flex: 1 },
  inputsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 18,
    paddingTop: 12,
  },
  nameRow: { flexDirection: 'row' },
  fName: { flex: 1, marginRight: 4 },
  lName: { flex: 1, marginLeft: 4 },
  button: { marginHorizontal: 0 },
});
export default ShopifyAddAddressWrapper;
