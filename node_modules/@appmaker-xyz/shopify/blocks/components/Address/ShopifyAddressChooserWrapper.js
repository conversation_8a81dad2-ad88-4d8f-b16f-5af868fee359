import React, { useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { useAddress, useAddressItem } from '../../../hooks/cart/useAddress';
import {
  ChooseAddress,
  AddressCard,
  EditAddress,
  useChooseAddress,
  Button,
  Layout,
} from '@appmaker-xyz/ui';
import { usePageState } from '@appmaker-xyz/core';
import { useUser } from '../../../hooks/user/useUser';
const AddressCardItem = ({
  onAction,
  address: propAddress,
  defaultAddressID,
  toggleModal,
}) => {
  const {
    address,
    selectAddress,
    addressSelectLoading,
    isDefault,
    editAddress,
  } = useAddressItem({
    onAction,
    address: propAddress,
    defaultAddressID,
  });
  return (
    <AddressCard
      name={`${address?.firstName} ${address?.lastName}`}
      address={address}
      formattedAddress={address?.formatted}
      onPress={async () => {
        await selectAddress();
      }}
      addressSelectLoading={addressSelectLoading}
      isDefault={isDefault}
      onEdit={() => {
        toggleModal();
        editAddress();
      }}
    />
  );
};
export function AddressModalContent({
  toggleAddressModal,
  addressList,
  openAddAddress,
  onAction,
  defaultAddressID,
}) {
  return (
    <>
      <Button
        title={'Add New Address'}
        icon={'plus'}
        containerStyles={{ marginBottom: 5 }}
        onPress={() => {
          toggleAddressModal();
          openAddAddress();
        }}
      />
      {addressList && addressList?.length > 0 ? (
        <Layout style={styles.separator} />
      ) : null}
      <ScrollView
        horizontal={true}
        contentContainerStyle={styles.scrollViewContentContainer}>
        {addressList
          ? addressList?.map?.((item) => {
              let address = item?.node;
              return (
                <AddressCardItem
                  toggleModal={toggleAddressModal}
                  address={address}
                  onAction={onAction}
                  defaultAddressID={defaultAddressID}
                />
              );
            })
          : null}
      </ScrollView>
    </>
  );
}
const ShopifyAddressChooserWrapper = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  ...props
}) => {
  const { isLoggedin } = useUser();
  const {
    addressList,
    defaultAddressID,
    openAddAddress,
    isAddressModalVisible,
    toggleAddressModal,
    setAddressModalVisible,
  } = useAddress({
    onAction,
  });

  const currentAddress =
    `${blockData?.shippingAddress?.address1 || ''} ${
      blockData?.shippingAddress?.address2 || ''
    } ${blockData?.shippingAddress?.city || ''}` || '';
  return isLoggedin ? (
    <ChooseAddress
      currentAddress={currentAddress}
      title={'Choose an \naddress'}
      isModalVisible={isAddressModalVisible}
      setModalVisible={setAddressModalVisible}
      openAddAddress={openAddAddress}>
      <AddressModalContent
        toggleAddressModal={toggleAddressModal}
        addressList={addressList}
        openAddAddress={openAddAddress}
        onAction={onAction}
        defaultAddressID={defaultAddressID}
      />
    </ChooseAddress>
  ) : null;
};
const styles = StyleSheet.create({
  scrollViewContentContainer: {
    paddingLeft: 16,
    marginBottom: 16,
  },
  separator: {
    height: 1,
    backgroundColor: '#ccc',
    margin: 16,
  },
});
export default ShopifyAddressChooserWrapper;
