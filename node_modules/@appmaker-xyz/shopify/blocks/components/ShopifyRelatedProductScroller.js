import React from 'react';
import { I18nManager } from 'react-native';
import { BlockCard } from '@appmaker-xyz/ui';
import ProductList from './product/ProductList';

export const ShopifyRelatedProductScroller = ({
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  ...props
}) => {
  const hideTitleBlock = attributes?.hideTitleBlock;
  const productId =
    attributes?.productId ||
    attributes?.config?.productId ||
    blockData?.node?.id ||
    attributes?.blockItem?.lines?.nodes?.[0]?.merchandise?.product?.id;

  const productTitle = blockData?.node?.title || '';
  const gridView = attributes?.gridViewListing || false;

  const finalAttributes = {
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    wishList: true,
    isInsideCartPage: !!attributes?.isInsideCartPage,
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
  };

  const FinalProductList = () => {
    return (
      <ProductList
        blockAttributes={finalAttributes}
        productRecommendationId={productId}
        useFlatlist
        horizontal={!gridView}
        disablePagination={true}
        surface="appmaker-related-product-scroller"
        referrer={{
          name: 'appmaker-related-product-scroller',
          type: 'product-scroller',
          title: `${productTitle}'s Related Products`,
        }}
        flashListProps={{
          style: {
            backgroundColor: attributes?.backgroundColor,
          },
          showsHorizontalScrollIndicator: false,
          ...(I18nManager.isRTL && {
            maintainVisibleContentPosition: {
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 0,
            },
          }),
        }}
        layoutProps={{
          loadingLayout: gridView ? 'product-grid' : 'product-scroller',
        }}
        {...props}
      />
    );
  };
  if (hideTitleBlock) {
    return <FinalProductList />;
  }
  return (
    <BlockCard
      {...props}
      attributes={{
        ...attributes,
        accessButton: attributes.showViewMoreButton ? attributes.ctaText : '',
        childContainerStyle: { paddingBottom: 12 },
      }}>
      <FinalProductList />
    </BlockCard>
  );
};
