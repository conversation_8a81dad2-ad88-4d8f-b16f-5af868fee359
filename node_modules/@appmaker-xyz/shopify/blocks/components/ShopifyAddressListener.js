import { View, Text } from 'react-native';
import React, { useEffect } from 'react';
import { usePageState } from '@appmaker-xyz/core';
import { Button } from '@appmaker-xyz/ui';
import { Countries, CountriesDefaultInfo } from '../../data/countriesStateData';
function getProvinces(provincesObject) {
  const provinceLabels = Object.keys(provincesObject);
  const provinceList = provinceLabels.map((item) => ({
    label: item,
    value: item,
  }));
  return provinceList;
}
const addressLabels = {
  address1: 'Address',
  address2: 'Apartment, suite, etc. (optional)',
  city: 'City',
  company: 'Company (optional)',
  country: 'Country/region',
  firstName: 'First name (optional)',
  lastName: 'Last name',
  phone: 'Phone (optional)',
  province: 'Region',
  zip: 'Postal code',
};
const {
  address_format: { items: addressFormatItem },
} = CountriesDefaultInfo;
// Countries
export default function ShopifyAddressListener({ attributes, coreDispatch }) {
  const { name = 'country' } = attributes;
  const parentName = attributes.parentName || '_formData';
  // console.log();
  useEffect(() => {
    coreDispatch({
      type: 'SET_VALUE',
      name: 'addressLabels',
      value: addressLabels,
    });
  }, []);
  const country = usePageState((state) => {
    return state[parentName] && state[parentName][name]
      ? state[parentName][name]
      : '';
  });
  // const countryInfo = Countries[country];
  // console.log(countryInfo);
  useEffect(() => {
    const countryInfo = Countries[country];
    if (countryInfo) {
      coreDispatch({
        type: 'SET_VALUE',
        name: 'address_format',
        value: addressFormatItem[countryInfo.address_format.edit],
      });
    }
    if (countryInfo && countryInfo.provinces) {
      const provinceList = getProvinces(countryInfo.provinces);
      coreDispatch({
        type: 'SET_VALUE',
        name: 'provinceList',
        value: provinceList,
      });
      coreDispatch({
        type: 'SET_VALUE',
        name: 'addressLabels',
        value: {
          ...addressLabels,
          ...countryInfo?.labels,
        },
      });
    }
  }, [country]);
  return null;
  return (
    <View>
      <Button
        title="Add Dummy address"
        onPress={() => {
          coreDispatch({
            type: 'SET_VALUE',
            name: '_formData',
            value: {
              firstName: 'John',
              lastName: 'Doe',
              address1: '123 Main St',
              address2: 'Apt. 2B',
              city: 'San Francisco',
              zip: '94107',
              country: 'United States',
              province: 'California',
              phone: '1234567890',
            },
          });
        }}
      />
      <Text>ShopifyAddressListener :- {country}</Text>
    </View>
  );
}
