import React, { useState } from 'react';
import { ActionBar, Layout } from '@appmaker-xyz/ui';
import { FlatList } from 'react-native';

const LanguageChooser = ({ attributes, pageDispatch, ...props }) => {
  const { items, selectedItem } = attributes;
  const [selectedLanguage, setSelectedLanguage] = useState(undefined);
  let default_key;
  if (!selectedLanguage) {
    default_key = selectedItem;
  }
  const renderItem = ({ item, index }) => {
    // const actionParams = {
    //   pageDispatch,
    //   values: {
    //     [selectedItemKey]: item.id,
    //   },
    // };
    if (item.id === default_key) {
      default_key = undefined;
      setSelectedLanguage(item.id);
    }
    const onPress = () => {
      default_key = undefined;
      props.onAction({
        action: 'SET_LANGUAGE',
        params: { language: item.id },
      });
      setSelectedLanguage(item.id);
    };
    return (
      <ActionBar
        onPress={onPress}
        attributes={{
          title: item.name,
          rightIcon: selectedLanguage == item.id ? 'check-circle' : 'circle',
        }}
      />
    );
  };
  return (
    <Layout>
      <FlatList
        data={items}
        renderItem={renderItem}
        // keyExtractor={(item) => item.id.toString()}
      />
    </Layout>
  );
};

export default LanguageChooser;
