import React from 'react';
import { usePluginStore } from '@appmaker-xyz/core';

const ShopifyProductDescription = ({
  attributes,
  BlocksView,
  innerBlocks,
  BlockItem,
  onAction,
  ...props
}) => {
  const { productDescription } = attributes;
  const shopifySettings = usePluginStore(
    (state) => state.plugins.shopify?.settings,
  );
  const { open_description_in_seperate_page } = shopifySettings || {
    open_description_in_seperate_page: false,
  };
  if (productDescription?.descriptionHtml) {
    if (open_description_in_seperate_page) {
      return (
        <BlockItem
          onAction={onAction}
          block={{
            clientId: 'product-description-webview',
            name: 'appmaker/actionbar',
            attributes: {
              containerStyle: {
                flex: 1,
              },
              title: 'Description',
              appmakerAction: {
                action: 'OPEN_IN_WEBVIEW',
                params: {
                  attributes: {
                    preHtml: `<meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <style type="text/css"> </style>`,
                  },
                  html: productDescription?.descriptionHtml,
                },
              },
              ...(attributes && attributes),
            },
          }}
        />
      );
    } else {
      return (
        <BlockItem
          onAction={onAction}
          block={{
            name: 'appmaker/expandable-text-block',
            attributes: {
              blockTitle: 'Description',
              content: productDescription?.descriptionHtml,
              accessButton: false,
              expanded: true,
              expandable: false,
            },
          }}
        />
      );
    }
  } else {
    return null;
  }
};

export default ShopifyProductDescription;
