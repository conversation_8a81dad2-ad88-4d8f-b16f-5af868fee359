import { usePageState } from '@appmaker-xyz/core';
import React, { useEffect } from 'react';
import { getAvailableOptions } from '../../helper/helper';
const ShopifyVariationListner = ({
  attributes,
  coreDispatch,
  currentAction,
  blockData
}) => {
  const { blockItem: product } = attributes;
  const selectedOptions = usePageState((state) => state.filter) || {};
  const handleOptionChange = () => {
    const selectedVariant = product?.node?.variants?.edges.find((variant) => {
      return variant.node.selectedOptions.every((selectedOption) => {
        return selectedOptions[selectedOption.name] === selectedOption.value;
      });
    });
    return selectedVariant;
  };
  let variant;
  const firstVariantAvailable = React.useMemo(() => {
    return product?.node?.variants?.edges.find((variant) => variant.node.availableForSale) || product?.node?.variants?.edges[0];
  }, [product]);
  try {
    variant = handleOptionChange() || firstVariantAvailable;
  } catch (error) {
    console.log(error);
  }
  useEffect(() => {
    if (variant) {
      coreDispatch({
        type: 'SET_PAGE_VAR',
        name: 'variant',
        value: { ...variant, variationSelected: selectedOptions },
      });
    }
  }, [variant]);
  useEffect(() => {
    if (product) {
      const availableOptions = getAvailableOptions({
        productData: product,
        // userSelectedOptions: selectedOptions,
      });
      coreDispatch({
        type: 'SET_PAGE_VAR',
        name: 'availableOptions',
        value: availableOptions,
      });
    }
  }, [selectedOptions, variant]);
  
  useEffect(() => {
    if (currentAction?.params?.selectedOptions) {
      coreDispatch({
        type: 'set_all_filter',
        filter: currentAction.params.selectedOptions,
      });
    } else if (currentAction?.params?.variantId) {
      const variants = blockData?.node?.variants?.edges || [];

      const selectedVariant = variants.find((variantObj) => {
        return variantObj.node.id === currentAction.params.variantId;
      });
      const filter = {};
      if (selectedVariant) {
        selectedVariant.node.selectedOptions.forEach((item) => {
          filter[item.name] = item.value;
        });
      }
      coreDispatch({
        type: 'set_all_filter',
        filter,
      });
    } else if (product?.node?.defaultSelectedOptions) {
      const filter = {};
      product.node.defaultSelectedOptions.map((item) => {
        filter[item.name] = item.value;
      });
      coreDispatch({
        type: 'set_all_filter',
        filter,
      });
    }
  }, [product, currentAction]);
  return null;
};
export { ShopifyVariationListner };
