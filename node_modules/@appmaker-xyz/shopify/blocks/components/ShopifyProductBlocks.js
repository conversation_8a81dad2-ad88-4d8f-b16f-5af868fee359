import { usePluginStore, appmaker } from '@appmaker-xyz/core';
import {
  appSettings,
  getExtensionAsBoolean,
  getExtensionConfigAsFloat,
} from '@appmaker-xyz/core';

import { Dimensions } from 'react-native';

import React from 'react';
import { applyFilters } from '@appmaker-xyz/core';

let deviceRatio =
  Dimensions.get('window').height / Dimensions.get('window').width;

export function ProductImage({ attributes, blockData, BlockItem, onAction }) {
  return (
    <BlockItem
      onAction={onAction}
      blockData={blockData}
      block={{
        name: 'appmaker/product-image',
        attributes: {
          appmakerAction: {
            action: 'OPEN_ZOOMABLE_IMAGE_SWIPER',
            params: {
              imageList: '{{blockItem.node.images.edges}}',
              indexImage: '{{pageState.variant.node.image.src}}',
            },
          },
          imageList: '{{blockItem}}',
          index: 0,
          savedItemIds: '{{pageState.metaData.savedItem.data}}',
          id: '{{blockItem.node.id}}',
          indexImage: '{{pageState.variant.node.image.src}}',
          title: '{{blockItem.node.title}}',
          uri: '{{blockItem.node.onlineStoreUrl}}',
          displayWishlist: true,
          displayShareButton: true,
          customDotColor: '',
          __appmakerStylesClassName: 'productDetailCustomStyles',
          ...(attributes && attributes),
        },
        dependencies: {
          pageState: ['variant', 'metaData'],
        },
      }}
    />
  );
}

export function ShopifyProductData({ attributes, BlockItem, onAction }) {
  return (
    <BlockItem
      onAction={onAction}
      block={{
        name: 'appmaker/product-data',
        clientId: 'product-data',
        attributes: {
          ...attributes,
          __appmakerStylesClassName: 'productDetailCustomStyles',
          // ...(attributes && attributes),
        },
        dependencies: {
          pageState: ['variant', 'metaData'],
        },
      }}
    />
  );
}

export function ShopifyProductPDPButtons({
  attributes,
  blockData,
  BlockItem,
  onAction,
}) {
  return (
    <BlockItem
      onAction={onAction}
      blockData={blockData}
      block={{
        name: 'appmaker/buttons',
        clientId: 'product-detail-buttons',
        attributes: {
          addToCartAction: appmaker.applyFilters(
            'add-to-cart-button-custom-action',
            false,
          ),
          showInReverseOrder: appSettings.getOptionAsBoolean(
            'display_product_detail_buttons_in_reverse_order',
          ),
          addCartButtonStyle: {
            backgroundColor: appSettings.getOption('primary_button_color'),
            borderWidth: 0,
          },
          addCartFontColor: appSettings.getOption('primary_button_text_color'),
          buyNowButtonStyle: {
            backgroundColor: appSettings.getOption('secondary_button_color'),
            borderWidth: 0,
          },
          buyNowFontColor: appSettings.getOption('secondary_button_text_color'),
          in_stock: '{{pageState.variant.node.availableForSale}}',
          addCartText: 'Add to Cart',
          buyNowText:
            '<% if(checkIfTrueFalse(plugins.shopify.settings.enable_buynow_in_pdp) == true){ %><%="Buy Now"%><% } %>',
          buyNowAction: {
            action: 'BUY_NOW',
            type: 'normal',
          },
          __appmakerStylesClassName: 'productDetailCustomStyles',
          ...(attributes && attributes),
        },
        dependencies: {
          pageState: ['variant'],
        },
      }}
    />
  );
}

export function ShopifyProductCustomizeButtons({
  attributes,
  blockData,
  BlockItem,
  onAction,
}) {
  return (
    <BlockItem
      onAction={onAction}
      blockData={blockData}
      block={{
        name: 'appmaker/customize-buttons',
        clientId: 'product-detail-buttons',
        attributes: {
          showInReverseOrder: appSettings.getOptionAsBoolean(
            'display_product_detail_buttons_in_reverse_order',
          ),
          addCartButtonStyle: {
            backgroundColor: appSettings.getOption('primary_button_color'),
            borderWidth: 0,
          },
          addCartFontColor: appSettings.getOption('primary_button_text_color'),
          buyNowButtonStyle: {
            backgroundColor: appSettings.getOption('secondary_button_color'),
            borderWidth: 0,
          },
          buyNowFontColor: appSettings.getOption('secondary_button_text_color'),
          in_stock: '{{pageState.variant.node.availableForSale}}',
          addCartText: 'Customize',
          buyNowText: 'Add to Cart',
          buyNowAction: {
            action: 'BUY_NOW',
            type: 'normal',
          },
          __appmakerStylesClassName: 'productDetailCustomStyles',
          ...(attributes && attributes),
        },
        dependencies: {
          pageState: ['variant'],
        },
      }}
    />
  );
}

export function ShopifyProductVariation({ attributes, BlockItem, onAction }) {
  return (
    <BlockItem
      onAction={onAction}
      block={{
        name: 'appmaker/product-variation',
        clientId: 'variation',
        attributes: {
          __appmakerAttributes: {
            emptyViewAttribute: {
              showEmptyView: false,
              message: 'No var',
            },
          },
          in_stock: '{{pageState.variant.node.availableForSale}}',
          dataSource: {
            repeatable: 'Yes',
            attributes: {
              params: '{{blockData}}',
              transformKey: 'shopify-detail-variations-v2',
            },
            source: 'variables',
            repeatItem: 'DataVariable',
          },
          blockTitle: '{{blockItem.name}}',
          title: '{{blockItem.name}}',
          variations: '{{blockItem.options}}',
          variationKey: '{{blockItem.key}}',
          defaultOptionValue: '{{blockItem.defaultValue}}',
          availableOptions: '{{pageState.availableOptions}}',
          __appmakerStylesClassName: 'productDetailCustomStyles',
          ...(attributes && attributes),
        },
      }}
    />
  );
}

export function ShopifyProductDescription({ attributes, BlockItem, onAction }) {
  const shopifySettings = usePluginStore(
    (state) => state.plugins.shopify?.settings,
  );
  const {
    hide_default_product_description,
    open_description_in_seperate_page,
  } = shopifySettings || {
    open_description_in_seperate_page: false,
    hide_default_product_description: false,
  };
  if (hide_default_product_description) {
    return null;
  }
  // console.log({show_description_in_product_detail_page});
  if (open_description_in_seperate_page) {
    return (
      <BlockItem
        onAction={onAction}
        block={{
          clientId: 'product-description-webview',
          name: 'appmaker/actionbar',
          attributes: {
            __display: '{{ blockItem.node.descriptionHtml !== "" }}',
            containerStyle: {
              flex: 1,
              marginBottom: 4,
            },
            title: 'Description',
            appmakerAction: {
              action: 'OPEN_IN_WEBVIEW',
              params: {
                attributes: {
                  preHtml: `<meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <style type="text/css"> </style>`,
                },
                html: '{{blockItem.node.descriptionHtml}}',
              },
            },
            ...(attributes && attributes),
          },
        }}
      />
    );
  } else {
    return (
      <BlockItem
        onAction={onAction}
        block={{
          name: 'appmaker/expandable-text-block',
          attributes: {
            __display: '{{ blockItem.node.descriptionHtml !== "" }}',
            blockTitle: 'Description',
            content: '{{blockItem.node.descriptionHtml}}',
            accessButton: false,
            expanded: true,
            expandable: false,
          },
        }}
      />
    );
  }
}

export function ShopifyProductListSlot({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
}) {
  const productListSlotBlock = {
    clientId: '48501bf5-1eba-4716-a5c0-c1e7e1e700db',
    name: 'appmaker/reusable-block',
    isValid: true,
    attributes: {
      blockId: 'shopify-product-list-item',
      blockAttributes: attributes,
    },
    innerBlocks: [],
  };
  // console.log(attributes);
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={productListSlotBlock}
    />
  );
}
export function ShopifyProductList({
  attributes,
  BlockItem,
  onAction,
  currentAction,
  BlocksView,
  innerBlocks,
  blockData,
}) {
  attributes.specialPrice = applyFilters('product-list-special-price', false);
  attributes.groceryMode =
    appSettings.getOptionAsBoolean('grocery_mode') || false;
  // let = false;
  const defaultDataSource = {
    source: 'shopify',
    attributes: {
      pageStateName: 'productList',
      mapping: {
        items: 'data.data.products.edges',
      },
      methodName: 'products',
      params: '{{ { ...currentAction.params, surface: "collection-page", } }}',
      allowFilters: true,
      filterVars: {
        pageState: ['filter', 'sort', 'searchQuery'],
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  if (
    !getExtensionAsBoolean(
      'shopify',
      'disable__experimental_load_product_list_after_block_data',
    ) === true
  ) {
    const canRunFn = ({ pageState }) => {
      return pageState?.blockData?.collection ||
        pageState?.blockData?.searchQuery ||
        pageState?.blockData?.search
        ? true
        : false;
    };
    defaultDataSource.runDefault = false;
    defaultDataSource.canRunFn = canRunFn;
    defaultDataSource.dependencies = {
      pageState: ['blockData'],
    };
  }
  const thresholdValue = getExtensionConfigAsFloat(
    'shopify',
    'shopify-flatlist-scroll-end-reached-threshold',
    4,
  );
  const finalAttributes = {
    loadingLayout: 'product-grid',
    wishList: true,
    savedItemIds: '{{(pageState.metaData.savedItem.data)}}',
    id: '{{blockItem.node.id}}',
    hasPages: true,
    surface: 'collection-page',
    referrer: {
      name: "{{blockData.searchQuery || blockData.search ? 'search-page': 'collection-page'}}", // To handle search and collection page for surface data
      type: 'product-list', // To handle search and collection page for surface data
      title:
        '{{blockData.searchQuery || blockData.search ? blockData.searchQuery || blockData.search : blockData.collection.title}}',
    },
    appmakerAction: {
      pageId: 'productDetail',
      params: {
        pageData: '{{blockItem}}',
      },
      action: 'OPEN_PRODUCT_DETAIL',
    },
    onEndReachedThreshold: thresholdValue,
    // cartQuantity: '{{getCartQty(appStorageState.checkout,blockItem.node.id)}}',
    title: '{{blockItem.node.title }}',
    numColumnsPageStateKey: 'numColumns',
    numColumnsDefault: deviceRatio > 1.6 ? '2' : '3',
    gridViewListing: true,
    uri: '{{blockItem.node.images.edges[0].node.url}}',
    productType:
      '{{blockItem.node.variants.edges.length > 1 ? "variable" : "normal"}}',
    in_stock:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    blockItem: '{{blockItem}}',
    titleNumberOfLines:
      '{{checkIfTrueFalse(plugins.shopify.settings.control_number_of_lines_in_product_list) == true ? (plugins.shopify.settings.number_of_lines_in_product_list) : "" }}',
    availableForSale:
      '{{blockItem.node.variants.edges.length==1?blockItem.node.variants.edges[0].node.availableForSale ? 1 : 0 : 1}}',
    regularPrice:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %> <% if( regularPrice>salePrice) { %> {{currencyHelper(regularPrice,blockItem.node.compareAtPriceRange.minVariantPrice.currencyCode)}}<% } %>',
    salePrice:
      '{{ currencyHelper(blockItem.node.priceRange.minVariantPrice.amount,blockItem.node.priceRange.maxVariantPrice.currencyCode)}}',
    onSale:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><%=echo(regularPrice>salePrice)%>',
    salePercentage:
      '<% const regularPrice = parseFloat(blockItem.node.compareAtPriceRange.minVariantPrice.amount) %><% const salePrice = parseFloat(blockItem.node.priceRange.minVariantPrice.amount) %><% if( regularPrice>salePrice) { %><%= parseInt(100-((salePrice/regularPrice)*100 ))%> <%="%"%>%><% } %>',
    thumbnail_meta: {
      height: '{{blockItem.node.images.edges[0].node.height}}',
      width: '{{blockItem.node.images.edges[0].node.width}}',
    },
    bestSeller: '{{blockItem.node.bestseller_tag}}',
    brandName:
      '<% if(checkIfTrueFalse(plugins.shopify.settings.show_vendor_pdp) == true){ %><%=blockItem.node.vendor%><% } %>',
    new_product: '{{blockItem.node.new_tag}}',
    brandColor: { primary: '#B6DCC9', secondary: '#E63F12' },
    saleBadgeStyle: {
      textStyle: {
        color: 'white',
      },
      containerStyle: {
        backgroundColor: '#E63F12',
      },
    },
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    show_last_few_remaining:
      '{{ blockItem.node.totalInventory < 5 && blockItem.node.totalInventory > 0 }}',
    last_few_remaining_text: 'Last Few Remaining',
    ...(attributes && attributes),
    dataSource: attributes.customDataSource
      ? attributes.customDataSource
      : defaultDataSource,
  };
  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      currentAction={currentAction}
      onAction={onAction}
      block={{
        name:
          attributes?.customProductGridItemName || 'appmaker/product-grid-item',
        innerBlocks,
        clientId: 'product-list',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
}
