import React from 'react';
import { View, Dimensions, StyleSheet, Platform } from 'react-native';
import { AppmakerVideoBlock } from '@appmaker-xyz/react-native';
import Icon from 'react-native-vector-icons/Feather';

const { width, height } = Dimensions.get('window');

const FullScreenVideo = (props) => {
  const videoData = props?.currentAction?.params;
  const videoSource = videoData?.source;
  const videoThumbnail = videoData?.thumbnail;
  return videoSource?.url ? (
    <View style={styles.innerContainer}>
      <Icon
        name="x"
        size={22}
        style={styles.icon}
        onPress={() => {
          props.onAction && props.onAction({ action: 'GO_BACK' });
        }}
        color="#ffffff"
      />
      <AppmakerVideoBlock
        attributes={{
          forceDefaultControls: true,
          style: {
            justifyContent: 'center',
            alignItems: 'center',
          },
          repeat: true,
          controls: true,
          video: {
            // previewUrl: videoThumbnail ? videoThumbnail : '',
            sourceUrl: videoSource?.url,
            meta: {
              height: videoSource?.height,
              width: videoSource?.width,
            },
          },
        }}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  innerContainer: {
    height,
    width,
    backgroundColor: 'black',
  },
  icon: {
    position: 'absolute',
    right: 18,
    top: Platform.OS === 'ios' ? 64 : 18,
    zIndex: 1,
    padding: 6,
    backgroundColor: '#00000066',
    borderRadius: 20,
    overflow: 'hidden',
  },
});

export default FullScreenVideo;
