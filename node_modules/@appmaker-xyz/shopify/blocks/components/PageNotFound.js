import React, { useEffect } from 'react';
import { EmptyBlock } from '@appmaker-xyz/ui';
import { analytics } from '@appmaker-xyz/core';

export default function PageNotFound(props) {
  const { attributes, currentAction } = props;
  useEffect(() => {
    analytics.trackSystemEvents('product_not_found', {
      pageId: currentAction?.pageId,
      action: currentAction,
    });
  }, []);

  return (
    <EmptyBlock
      attributes={attributes}
      onPress={() => {
        props.onAction({
          action: 'GO_BACK',
        });
      }}
    />
  );
}