import React from 'react';
import { HeaderNotice } from '@appmaker-xyz/ui';
import { useCart } from '@appmaker-xyz/shopify';

const ShopifyHeaderNotice = (props) => {
  const { cartTotalSavingsWithCurrency, cartTotalSavings, totalQuantity } =
    useCart();
  return cartTotalSavings > 0 && totalQuantity > 0 ? (
    <HeaderNotice
      {...props}
      testId={'header-notice'}
      attributes={{
        __display: totalQuantity > 0,
        text: `You saved ${cartTotalSavingsWithCurrency} on this order.`,
        totalSaved: cartTotalSavingsWithCurrency,
        ...props.attributes,
      }}
    />
  ) : null;
};
export default ShopifyHeaderNotice;
