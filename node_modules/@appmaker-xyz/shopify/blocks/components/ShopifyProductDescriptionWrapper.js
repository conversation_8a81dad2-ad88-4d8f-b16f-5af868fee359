import React from 'react';
const ShopifyProductDescriptionWrapper = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  ...props
}) => {
  const productId = blockData?.node?.id;
  const defaultDataSource = {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.product',
      },
      methodName: 'getProductDescription',
      params: {
        productId,
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  const finalAttributes = {
    viewSingle: true,
    productDescription: '{{blockItem}}',
    product: blockData,
    productId: productId,
    // showLoadingTillData: true,
    loadingLayout: 'normal',
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
    dataSource: defaultDataSource,
  };

  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      // currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'shopify/product-description-v2',
        innerBlocks,
        clientId: 'product-description-v2',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
};

export default ShopifyProductDescriptionWrapper;
