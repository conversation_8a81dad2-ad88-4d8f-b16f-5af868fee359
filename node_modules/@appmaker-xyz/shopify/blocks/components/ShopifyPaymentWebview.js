import React, { useState, useEffect, useRef } from 'react';
import { WebView } from 'react-native-webview';
import {
  View,
  Text,
  StyleSheet,
  Platform,
  Dimensions,
  ActivityIndicator,
  BackHandler,
} from 'react-native';
import { HeaderBackButton } from '@react-navigation/elements';
import { useNavigation } from '@react-navigation/native';
// import Config from 'Config';
import { Layout, LayoutIcon } from '@appmaker-xyz/ui';
import { webViewCanHandleCurrentUrl } from '@appmaker-xyz/react-native';
import {
  usePluginStore,
  applyFilters,
  appSettings,
  getExtensionAsBoolean,
} from '@appmaker-xyz/core';
import { webViewHandleOnMessage } from '@appmaker-xyz/react-native';

// import AutoHeightWebView from './AutoHeightWebView.js';
let { width, height } = Dimensions.get('window');
const LoadingBar = ({ color = '#3B78E7', percent, height = 3 }) => {
  console.log(`${parseInt(percent) * 100}%`);
  const style = {
    backgroundColor: color,
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[style]} />;
};

const ShopifyPaymentWebview = ({ attributes = {}, onAction, ...props }) => {
  const [progress, setProgress] = useState(0);
  const [isLoaded, setLoaded] = useState(false);
  const { navigationFilter, urlListener, loadingLayout } = attributes;
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  let [webviewSource, setWebviewSource] = useState({});
  const [redirectLoading, setRedirectLoading] = useState(true);
  const [isCheckoutComplete, setCheckoutComplete] = useState(false);
  const [progressBarVisibility, setProgressBarVisibility] = useState(true);
  const webview = useRef();
  const navigation = useNavigation();
  const disableWebViewEventHandling = appSettings.getExtensionOptionAsBoolean(
    'shopify',
    'disable_webview_event_handling',
    false,
  );
  const isMultiWindowDisabled = getExtensionAsBoolean?.(
    'shopify',
    'disable_multi_window_payemnt_webview',
    false,
  );
  navigation &&
    navigation.setOptions({
      headerLeft: (props) => {
        const appBackButtonName = applyFilters('app-back-button-icon', false);
        const onWebViewBackPress = () => {
          if (isCheckoutComplete) {
            onAction({
              action: 'RESET',
              replacePage: true,
            });
          } else if (canGoBack) {
            webview.current.goBack();
          } else {
            navigation.goBack();
          }
        };
        if (
          attributes.backButtonIconName ||
          Object.keys(appBackButtonName).length
        ) {
          return (
            <LayoutIcon
              clientId="back-button"
              attributes={{
                svgXml: attributes.svgXml || appBackButtonName.svgXml,
                svgIcon: attributes.svgIcon || appBackButtonName.svgIcon,
                iconName:
                  attributes.backButtonIconName || appBackButtonName.name,
                iconColor: appSettings.getOption('toolbar_text_color'),
              }}
              onPress={onWebViewBackPress}
            />
          );
        }
        return <HeaderBackButton {...props} onPress={onWebViewBackPress} />;
      },
    });
  const setLoadingBarVisibility = (visibility) => {
    if (visibility !== progressBarVisibility) {
      setProgressBarVisibility(visibility);
    }
  };
  useEffect(() => {
    urlListener && urlListener(currentUrl, onAction, webview);
  }, [currentUrl, urlListener, onAction]);

  useEffect(() => {
    let { source } = attributes;
    if (attributes.redirectURL) {
      onAction(attributes.redirectAction)
        .then((url) => {
          setWebviewSource({ uri: url });
          setRedirectLoading(false);
        })
        .catch((error) => {
          setWebviewSource(source);
          setRedirectLoading(false);
        });
    } else {
      setRedirectLoading(false);
      if (source && typeof source !== 'object') {
        webviewSource[source] = attributes[source];
        setWebviewSource(webviewSource);
      } else {
        setWebviewSource(source);
      }
      if (source === 'html') {
        webviewSource.html = `${attributes.preHtml ? attributes.preHtml : ''}${
          webviewSource.html
        }${attributes.postHtml ? attributes.postHtml : ''}`;
        setWebviewSource(webviewSource);
      }
    }
  }, [attributes.source]);

  const [loading, setLoading] = useState(true);
  let extraProps = {};

  const shopifySettings = usePluginStore(
    (state) => state?.plugins?.shopify?.settings,
  );
  if (shopifySettings?.useIncognitoMode == true) {
    extraProps = { ...extraProps, incognito: true };
  }

  if (props.url) {
    extraProps = {
      mediaPlaybackRequiresUserAction: true,
      startInLoadingState: true,
    };
  }

  if (Platform.version > 16) {
    extraProps.mediaPlaybackRequiresUserAction = true;
  }
  let userAgentProps = {};
  if (attributes?.userAgent) {
    userAgentProps = {
      userAgent: attributes.userAgent,
    };
  }
  const backButtonHandler = () => {
    if (canGoBack) {
      webview.current.goBack();
      return true; // prevent default behavior (exit app)
    }
    return false;
  };
  useEffect(() => {
    const dimensionsHandler = BackHandler.addEventListener('hardwareBackPress', backButtonHandler);

    return () => dimensionsHandler.remove();
  }, [backButtonHandler]);
  // let containerStyle = {height: attributes.height};
  let containerStyle = attributes.height && {
    height: Number.parseInt(attributes.height),
  };
  const LoadingView = () => (
    <View style={styles.loadingContainerStyle}>
      <Text>Loading.....</Text>
    </View>
  );

  const timeIntervalCheckOfURLScript = `
  (function() {
    let lastUrl = window.location.href;
    
    function checkForUrlChange() {
      if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        window.ReactNativeWebView.postMessage(JSON.stringify({type: 'urlChange', url: lastUrl}));
      }
    }

    // Check for changes every 500ms
    setInterval(checkForUrlChange, 500);

    // Also check on popstate events
    window.addEventListener('popstate', checkForUrlChange);
    window.addEventListener('hashchange', checkForUrlChange);
  })();`;

  const appmakerWebScript = `
  window.appmakerWebview = { platform: "${Platform.OS}" };
  ${
    shopifySettings?.checkout_webview_custom_script
      ?.activate_time_interval_check_script_for_checkout_url
      ? timeIntervalCheckOfURLScript
      : ''
  }
  ${
    shopifySettings?.checkout_webview_custom_script
      ?.activate_custom_script_in_checkout_webview
      ? shopifySettings?.checkout_webview_custom_script
          ?.custom_script_in_checkout_webview
      : ''
  }
  ${attributes?.injectedJavaScript ? attributes.injectedJavaScript : ''}
  true; // note: this is required, or you'll sometimes get silent failures
`;

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'urlChange') {
        console.log('#URL changed from onMessage:', data.url);
        setCurrentUrl(data.url);
        urlListener && urlListener(data.url, onAction, webview);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
    }

    // Call the existing onMessage handler
    if (!disableWebViewEventHandling) {
      webViewHandleOnMessage(event, {
        onAction,
        url: currentUrl,
      });
    }
  };
  const socialMediaFilter = ({ url }) => {
    onAction({
      action: 'OPEN_URL',
      params: {
        url,
      },
    });
    return false;
  };
  const checkIfOrderComplete = (navState) => {
    const regexThankyouPage = /checkouts\/(.*)\/thank[-_]you/;
    if (regexThankyouPage.test(navState?.url)) {
      !isCheckoutComplete && setCheckoutComplete(true);
      canGoBack && setCanGoBack(false);
      return true;
    }
    return false;
  };

  function onShouldStartLoadWithRequest(navState) {
    const isOrderComplete = checkIfOrderComplete(navState);
    if (
      webViewCanHandleCurrentUrl &&
      webViewCanHandleCurrentUrl(
        navState,
        onAction,
        {},
        { webViewRef: webview, setLoadingBarVisibility },
      )
    ) {
      return false;
    }
    !isOrderComplete && setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setCurrentUrl(navState.url);
    navigationFilter && navigationFilter(navState, onAction);

    return true;
  }
  return (
    <Layout style={[{ flex: 1 }, containerStyle]} loading={false}>
      {!isLoaded && progressBarVisibility ? (
        <LoadingBar percent={progress} color="#ff8300" />
      ) : null}
      {(webviewSource.uri || webviewSource.html) && (
        <WebView
          onLoadProgress={({ nativeEvent }) =>
            progressBarVisibility && setProgress(nativeEvent.progress)
          }
          onLoadEnd={() => setLoaded(true)}
          // onLoadStart
          showsVerticalScrollIndicator={false}
          ref={webview}
          injectedJavaScript={appmakerWebScript}
          // injectJavaScript={'alert("hello ")'}

          {...extraProps}
          {...userAgentProps}
          useWebKit={true}
          domStorageEnabled={true}
          originWhitelist={['*']}
          setSupportMultipleWindows={!isMultiWindowDisabled}
          androidHardwareAccelerationDisabled={false} // this is added to resolve a crash happened in tab switching in some devices (libc.so)
          automaticallyAdjustContentInsets={true}
          style={styles.containerWebView}
          allowsInlineMediaPlayback={true}
          sharedCookiesEnabled={true}
          source={webviewSource}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          onLoadStart={() => {
            setLoading(true);
            setLoaded(false);
          }}
          onShouldStartLoadWithRequest={onShouldStartLoadWithRequest}
          onNavigationStateChange={(navState) => {
            if (typeof navState.url === 'string') {
              const isOrderComplete = checkIfOrderComplete(navState);
              !isOrderComplete && setCanGoBack(navState.canGoBack);
              setCanGoForward(navState.canGoForward);
              setCurrentUrl(navState.url);
              navigationFilter && navigationFilter(navState, onAction);
            }
          }}
          onLoad={() => {
            setLoading(false);
          }}
          cacheEnabled={true}
          // cacheMode="LOAD_CACHE_ELSE_NETWORK"
          startInLoadingState={true}
          // renderLoading={LoadingView}
        />
      )}
      {/* {loading && <LoadingView />} */}
    </Layout>
  );
};

const styles = StyleSheet.create({
  containerWebView: {
    flex: 1,
  },
  loadingContainerStyle: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: width,
    height: height,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
});
export default ShopifyPaymentWebview;
