import React from 'react';

const ShopifyReviewList = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  ...props
}) => {
  const productId = blockData?.productId;
  const defaultDataSource = {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.judgemeReviews.reviews',
      },
      methodName: 'getReviews',
      params: {
        productId,
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  const finalAttributes = {
    hasPages: true,
    loadingLayout: 'normal',
    saleBadgeStyle: {
      textStyle: {
        color: 'white',
      },
      containerStyle: {
        backgroundColor: '#E63F12',
      },
    },
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
    dataSource: attributes.customDataSource
      ? attributes.customDataSource
      : defaultDataSource,
  };

  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      // currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'shopify/review-item',
        innerBlocks,
        clientId: 'review-item',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
};

export default ShopifyReviewList;
