import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { ThemeText } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/AntDesign';

const ShowAllReviewButton = ({ blockData, onAction }) => {
  const productId = blockData?.productId || blockData?.node?.id;
  const onPress = () => {
    onAction &&
      onAction({
        action: 'OPEN_REVIEWS_LIST',
        params: {
          pageData: {
            productId,
          },
        },
      });
  };
  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onPress} style={styles.viewAllButton}>
        <ThemeText fontFamily="bold">See All Reviews</ThemeText>
        <Icon name="right" size={14} color="#333" style={styles.arrowIcon} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  viewAllButton: {
    paddingVertical: 10,
    borderColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  arrowIcon: {
    marginLeft: 5,
  },
  container: {
    backgroundColor: '#fff',
    marginBottom: 4,
  },
});

export default ShowAllReviewButton;
