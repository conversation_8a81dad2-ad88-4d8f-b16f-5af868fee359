import React from 'react';
import { View, FlatList } from 'react-native';
import { useDataSourceV2 } from '@appmaker-xyz/core';
import { useFocusEffect } from '@react-navigation/native';

const ShopifyReviewListSummery = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  BlockItemRender,
  ...props
}) => {
  const productId = blockData?.productId || blockData?.node?.id;
  const dataSource = {
    source: 'shopify',
    methodName: 'getReviews',
    dataExtractor: (data) => data?.data?.data?.judgemeReviews?.reviews,
    params: {
      default: false,
      productId,
      limit: 5,
    },
  };
  const [queryResult, { item }] = useDataSourceV2({
    dataSource: dataSource,
    enabled: true,
  });

  useFocusEffect(
    React.useCallback(() => {
      queryResult.refetch();
    }, []),
  );

  const renderItem = ({ item }) => {
    return (
      <BlockItem
        BlockItem={BlockItem}
        BlocksView={BlocksView}
        blockData={item}
        onAction={onAction}
        block={{
          name: 'shopify/review-item',
          innerBlocks,
          clientId: 'review-item',
          isValid: true,
        }}
      />
    );
  };
  return item?.length > 0 ? (
    <View>
      <FlatList
        key={'review-summery-list'}
        numColumns={1}
        data={item}
        renderItem={renderItem}
        keyExtractor={(item, index) => item?.updated_at?.toString()}
      />
      <BlockItem
        BlockItem={BlockItem}
        BlocksView={BlocksView}
        blockData={{
          productId,
        }}
        onAction={onAction}
        block={{
          name: 'shopify/show-all-review',
          innerBlocks,
          clientId: 'review-show-all',
          isValid: true,
        }}
      />
    </View>
  ) : null;
};

export default ShopifyReviewListSummery;
