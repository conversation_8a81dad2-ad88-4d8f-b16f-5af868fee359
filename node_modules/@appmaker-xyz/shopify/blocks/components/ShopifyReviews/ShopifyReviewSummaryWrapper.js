import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
const ShopifyReviewSummaryWrapper = ({
  pageData,
  attributes,
  BlocksView,
  innerBlocks,
  blockData,
  BlockItem,
  onAction,
  ...props
}) => {
  const productId = blockData?.node?.id;
  const defaultDataSource = {
    source: 'shopify',
    attributes: {
      mapping: {
        items: 'data.data.judgemeReviewCount',
      },
      reloadOnFocus: true,
      methodName: 'getReviewSummary',
      params: {
        productId,
      },
    },
    repeatable: 'Yes',
    repeatItem: 'DataSource',
  };
  const finalAttributes = {
    viewSingle: true,
    summary_data: '{{blockItem}}',
    productId: productId,
    // showLoadingTillData: true,
    loadingLayout: 'normal',
    __appmakerStylesClassName: 'productGridWidgetCustomStyles',
    ...(attributes && attributes),
    dataSource: defaultDataSource,
  };

  return (
    <BlockItem
      BlockItem={BlockItem}
      BlocksView={BlocksView}
      // currentAction={currentAction}
      onAction={onAction}
      block={{
        name: 'shopify/review-summery',
        innerBlocks,
        clientId: 'review-summery',
        isValid: true,
        attributes: finalAttributes,
      }}
    />
  );
};

export default ShopifyReviewSummaryWrapper;
