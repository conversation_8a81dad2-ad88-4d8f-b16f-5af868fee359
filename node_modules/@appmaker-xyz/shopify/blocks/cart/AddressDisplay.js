import React from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText, Button } from '@appmaker-xyz/ui';

const AddressDisplay = ({
  attributes = {},
  onPress,
  onEditPress,
  onAction,
  coreDispatch,
}) => {
  const { title, subTitle, appmakerAction, appmakerEditAction, loading } =
    attributes;
  let onPressHandle = onPress;
  if (appmakerAction && onAction) {
    onPressHandle = () => onAction(appmakerAction);
  }
  let onPressEditHandle = onEditPress;
  if (appmakerAction && onAction) {
    onPressEditHandle = () => onAction(appmakerEditAction);
  }

  return (
    <Layout style={styles.container}>
      <Layout style={styles.textContainer}>
        <ThemeText size="lg" fontFamily="medium">
          {title}
        </ThemeText>
        {subTitle ? <ThemeText numberOfLines={1}>{subTitle}</ThemeText> : null}
      </Layout>
      <Button
        small
        outline
        status="demiDark"
        onPress={onPressHandle}
        loading={loading}>
        {subTitle ? 'Change' : 'Choose'}
      </Button>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#FFF',
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderTopColor: '#E9EDF1',
    borderTopWidth: 1,
  },
  textContainer: { marginRight: 4, flex: 1 },
});

export default AddressDisplay;
