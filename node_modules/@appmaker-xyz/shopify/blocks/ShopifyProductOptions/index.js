import { View } from 'react-native';
import React from 'react';
import { useProductOptions } from '@appmaker-xyz/shopify';
import { ProductOptions } from '@appmaker-xyz/ui';

export default function ShopifyProductOptions(props) {
  const {
    variationOptions,
    setOption,
    selectedOptions,
    product,
    isOptionAvilable,
    isOptionAvailable,
  } = useProductOptions(props);
  return (
    <View>
      {variationOptions.map((variationOption) => {
        return (
          <View key={`options-${variationOption.key}`}>
            <ProductOptions
              name={variationOption.name}
              isOptionAvailable={(value) =>
                isOptionAvailable(variationOption.name, value)
              }
              options={variationOption.options}
              product={product}
              selectedOption={
                selectedOptions && selectedOptions[variationOption?.key]
              }
              clickExpand={() => {
                // Click
              }}
              setOption={(item) => {
                setOption(variationOption.key, item.value);
              }}
            />
          </View>
        );
      })}
    </View>
  );
}
