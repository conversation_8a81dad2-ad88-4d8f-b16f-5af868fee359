fragment SimpleProductFragment on Product {
  id
  title
  tags
  handle
  totalInventory
  # description
  # descriptionHtml
  onlineStoreUrl
  productType
  vendor
  availableForSale
  media(first: 250) {
    edges {
      node {
        mediaContentType
        ... on Video {
          id
          mediaContentType
          sources {
            url
            height
            format
            width
          }
          previewImage {
            originalSrc: url(transform: {})
          }
        }
        ... on MediaImage {
          id
          mediaContentType
          image {
            height
            id
            originalSrc: url(transform: {})
            width
            url(transform: { maxHeight: 500,})
          }
        }
      }
    }
  }
  priceRange {
    maxVariantPrice {
      amount
      currencyCode
    }
    minVariantPrice {
      amount
      currencyCode
    }
  }
  compareAtPriceRange {
    maxVariantPrice {
      amount
      currencyCode
    }
    minVariantPrice {
      amount
      currencyCode
    }
  }
  options {
    id
    name
    values
  }
  appmaker_hide_product_status: metafield(
    namespace: "appmaker"
    key: "hide_product_status"
  ) {
    value
  }
  variants(first: 250) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    edges {
      node {
       ...SimpleProductVariantFragment
      }
    }
  }
  images(first: 250) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    edges {
      node {
        altText
        src: url(transform: {})
        height
        width
        url(transform: { maxWidth: 540 })
      }
    }
  }
}
