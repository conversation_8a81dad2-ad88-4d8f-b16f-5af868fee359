import { useEffect, useReducer, useState, useMemo } from 'react';
import { isEmpty } from 'lodash';
import {
  runDataSource,
  appmaker,
  usePageState,
  applyFilters,
} from '@appmaker-xyz/core';
import { Platform } from 'react-native';
import { useCart } from './useCart';
import { currencyHelper } from '../../helper/index';
import { getSavingsValue, calculateUpdatedCartSubTotal } from './helper';
import { isCartApiEnabled } from '../../helper/helper';

const initialState = {
  couponsList: [],
  couponsListLoading: true,
  couponsListError: null,
  isCoupon: false,
  couponCode: '',
  isCouponApplyingLoading: false,
  isCouponRemovingLoading: false,
  couponMessage: '',
};
function reducer(state, action) {
  switch (action.type) {
    case 'SET_COUPON':
      return {
        ...state,
        coupon: action.payload,
      };
    case 'SET_COUPON_CODE':
      return {
        ...state,
        couponCode: action.payload,
      };
    case 'SET_COUPON_APPLYING':
      return {
        ...state,
        isCouponApplyingLoading: action.payload,
        couponMessage: action.message,
      };
    case 'SET_COUPON_REMOVING':
      return {
        ...state,
        isCouponRemovingLoading: action.payload,
      };
    case 'SET_COUPON_MESSAGE':
      return {
        ...state,
        couponMessage: action.payload,
      };
    case 'SET_COUPON_LIST':
      return {
        ...state,
        couponsList: action.payload,
        couponsListLoading: false,
      };
    case 'SET_COUPON_LIST_ERROR':
      return {
        ...state,
        couponsListLoading: false,
        couponsListError: action.payload,
      };
    default:
      return state;
  }
}
function getSavingsMessage(discountCode, cartTotal = 1000) {
  if (!discountCode?.applicable) {
    return 'Sorry, this discount code is not applicable.';
  }

  let savings = 0;
  if (discountCode.value.amount) {
    savings = parseFloat(discountCode.value.amount);
  }
  if (discountCode.value.percentage) {
    savings = cartTotal * (discountCode.value.percentage / 100);
  }
  if (savings === 0) {
    return '';
  }
  savings = savings.toFixed(2);
  const savingsWithCurrency = currencyHelper(savings);
  if (
    discountCode.allocationMethod === 'ACROSS' &&
    discountCode.targetSelection === 'ALL'
  ) {
    return `Saved ${savingsWithCurrency}`;
  } else if (
    discountCode.allocationMethod === 'ACROSS' &&
    discountCode.targetSelection === 'SELECTED'
  ) {
    // return `Saved ${savingsWithCurrency}`;
  } else if (discountCode.allocationMethod === 'EACH') {
    // return `Saved ${savingsWithCurrency}`;
  }
  return '';
}

function getDiscountCodeApplication(couponDiscounted) {
  let discountCodeApplication;
  try {
      discountCodeApplication = couponDiscounted?.find?.(
        (item) =>
          item?.node?.__typename === 'DiscountCodeApplication' &&
          item?.node?.applicable === true,
      );
      discountCodeApplication = discountCodeApplication?.node;
  } catch (error) {}
  return discountCodeApplication;
}
function getAutomaticDiscountApplication(couponDiscounted) {
  let automaticDiscountApplication;
  try {
    automaticDiscountApplication = couponDiscounted.find(
      (item) => item.node.__typename === 'AutomaticDiscountApplication',
    );
    automaticDiscountApplication = automaticDiscountApplication.node;
  } catch (error) {}
  return automaticDiscountApplication;
}

function getLineAutomaticDiscountApplication(lines) {
  let automaticDiscountApplicationLine;
  let automaticDiscountApplication;
  try {
    automaticDiscountApplicationLine = lines?.edges?.find(
      (item) => {
        return item?.node?.discountAllocations?.some?.(discountAllocation => discountAllocation?.discountApplication?.__typename === 'AutomaticDiscountApplication')
      }
    );
    if(automaticDiscountApplicationLine) {
      automaticDiscountApplication = automaticDiscountApplicationLine?.node?.discountAllocations?.find(item => item?.discountApplication?.__typename === 'AutomaticDiscountApplication');
    }
  } catch (error) {}
  return automaticDiscountApplication;
}
export function useDiscount(props) {
  const canShowSuccessMessage = usePageState(
    (state) => state.canShowSuccessMessage,
  );
  const isCartApi = isCartApiEnabled();
  const setState = usePageState((state) => state.setState);
  const { onAction, pageDispatch } = props;
  const {
    cart,
    cartSubTotalAmount,
    totalQuantity: cartTotalQuantity,
    lineItems,
  } = useCart();
  const [state, dispatch] = useReducer(reducer, initialState);
  let appliedDiscountCodeItem = getDiscountCodeApplication(
    cart?.discountApplications?.edges,
  );

  const appliedLineAutomaticDiscountItem = getLineAutomaticDiscountApplication(
    cart?.lineItems
  );

  // TODO: Applied discount should be an array instead of a single object
  const appliedAutomaticDiscountItem = getAutomaticDiscountApplication(
    cart?.discountApplications?.edges,
  ) || appliedLineAutomaticDiscountItem;
  const disableCalculations = appmaker.applyFilters(
    'app-coupon-disable-calculations',
    false,
  );
  const couponTitle = appliedDiscountCodeItem
    ? appliedDiscountCodeItem?.code
    : '';
  function setSuccessMessageDisplayStatus(visible, message = '') {
    setState((draft) => {
      draft.canShowSuccessMessage = visible;
      draft.couponSuccessMessage = message;
    });
  }
  const setSuccessMessage = () => setSuccessMessageDisplayStatus(true);
  const setCouponModalVisibility = (isVisible) =>
    setState((draft) => {
      draft.applyCouponModalVisible = isVisible;
    });
  function dissmissSuccessMessage() {
    setSuccessMessageDisplayStatus(false, '');
  }
  const openCouponList = () => {
    onAction({
      action: 'OPEN_CART_COUPON_LIST',
    });
  };
  async function onApplyCoupon(couponCode, extraParams = {}) {
    try {
      const action = {
        action: 'APPLY_COUPON',
        params: {
          coupon: couponCode.trim(),
          ...extraParams,
        },
      };
      dispatch({ type: 'SET_COUPON_APPLYING', payload: true, message: '' });
      const response = await onAction(action);
      const { error } = response;
      if (isEmpty(error)) {
        // sleep for 500ms to show success message
        setCouponModalVisibility(false);
        if (Platform.OS === 'ios') {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
        setSuccessMessage(true);
      }
      dispatch({
        type: 'SET_COUPON_APPLYING',
        payload: false,
        message: 'Coupon Applied Successfully',
      });
      return response;
    } catch (error) {
      dispatch({
        type: 'SET_COUPON_APPLYING',
        payload: false,
        message: 'Something went wrong!',
      });
      console.log('coupon input error', error);
    }
  }

  async function onApplyCouponList(couponCodeList, extraParams = {}) {
    try {
      const action = {
        action: 'APPLY_COUPON',
        params: {
          couponList: couponCodeList,
          ...extraParams,
        },
      };
      dispatch({ type: 'SET_COUPON_APPLYING', payload: true, message: '' });
      const response = await onAction(action);
      const { error } = response;
      if (isEmpty(error)) {
        // sleep for 500ms to show success message
        setCouponModalVisibility(false);
        if (Platform.OS === 'ios') {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
        setSuccessMessage(true);
      }
      dispatch({
        type: 'SET_COUPON_APPLYING',
        payload: false,
        message: 'Coupon Applied Successfully',
      });
      return response;
    } catch (error) {
      dispatch({
        type: 'SET_COUPON_APPLYING',
        payload: false,
        message: 'Something went wrong!',
      });
      console.log('coupon input error', error);
    }
  }
  async function onRemoveCoupon(couponCode) {
    const action = {
      action: 'REMOVE_COUPON',
      params: {
        coupon: couponCode,
      },
    };
    dispatch({ type: 'SET_COUPON_REMOVING', payload: true });
    const couponResp = await onAction(action);
    // setBlockData(couponResp);
    // pageDispatch({
    //   type: 'set_value',
    //   name: 'cartResponse',
    //   value: couponResp,
    // });
    dispatch({ type: 'SET_COUPON_REMOVING', payload: false });
  }
  useEffect(() => {
    runDataSource(
      {
        dataSource: {
          source: 'appmaker-filter-source',
          attributes: {},
        },
      },
      {
        methodName: 'getData',
        params: {
          filterName: 'shopify-coupons-list',
          input: [],
        },
      },
    )
      .then(([res]) => {
        const data = checkCouponValidity({
          data: res,
          cartTotalQuantity,
          cartSubTotalAmount,
          lineItems,
        });
        dispatch({ type: 'SET_COUPON_LIST', payload: data });
      })
      .catch((err) => {
        dispatch({ type: 'SET_COUPON_LIST_ERROR', payload: err });
      });
  }, [cartSubTotalAmount, cartTotalQuantity]);

  const couponTitleDisplay = appmaker.applyFilters(
    'appmaker-coupon-title-display',
    couponTitle,
  );
  let sortedCouponList = useMemo(
    () => sortDiscountCode(state.couponsList),
    [state.couponsList],
  );
  return {
    cartSubTotalAmount,
    couponTitle,
    couponTitleDisplay,
    couponMessage: state.couponMessage,
    appliedDiscountCodeItem,
    appliedAutomaticDiscountItem,
    openCouponList,
    couponsList: sortedCouponList || state.couponsList,
    couponsListLoading: state.couponsListLoading,
    onApplyCoupon,
    onApplyCouponList,
    onRemoveCoupon,
    hasCouponApplied: !isEmpty(appliedDiscountCodeItem),
    hasAutomaticDiscountApplied: !isEmpty(appliedAutomaticDiscountItem),
    isCouponApplyingLoading: state.isCouponApplyingLoading,
    isCouponRemovingLoading: state.isCouponRemovingLoading,
    canShowSuccessMessage,
    dissmissSuccessMessage,
    setSuccessMessage,
    cartTotalQuantity,
  };
}

const checkCouponValidity = ({
  data,
  cartSubTotalAmount,
  cartTotalQuantity,
  lineItems,
}) => {
  const disableCalculations = appmaker.applyFilters(
    'app-coupon-disable-calculations',
    false,
  );
  const enableAllCoupons = appmaker.applyFilters(
    'app-coupon-enable-all-coupons',
    false,
  );
  const newData = data?.map((couponItem) => {
    let coupon = { ...couponItem };
    const updatedcartSubTotalAmount = calculateUpdatedCartSubTotal({
      lineItems,
      coupon,
      cartSubTotalAmount,
    });
    const dataC = appmaker.applyFilters(
      'app-coupon-is-valid',
      {
        valid: true,
        message: false,
      },
      {
        coupon: coupon,
        cartSubTotalAmount: updatedcartSubTotalAmount ? updatedcartSubTotalAmount : cartSubTotalAmount,
        cartTotalQuantity,
        disableCalculations,
        enableAllCoupons,
        lineItems,
      },
    );
    coupon.isValid = dataC?.valid;
    coupon.validMessage = dataC?.message;
    let calculatedSavingsValue = getSavingsValue?.({
      discountType: coupon.value_type,
      discountValue: coupon.value,
      maxDiscountValue: coupon.max_discount_value,
      subtotal: updatedcartSubTotalAmount ? updatedcartSubTotalAmount :cartSubTotalAmount,
    });
    coupon.calculatedSavingsValue = calculatedSavingsValue;
    return coupon;
  });
  return newData;
};

const sortDiscountCode = (couponList) => {
  const sortedCoupons = applyFilters('coupon-list-sort', couponList);
  if (sortedCoupons) {
    return sortedCoupons;
  }
  return sortedCoupons;
};
