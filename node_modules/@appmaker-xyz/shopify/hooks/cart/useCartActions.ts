import { useState } from 'react';
import { usePageState } from '@appmaker-xyz/core';
import { handleAction } from '@appmaker-xyz/react-native';
import { NavigationAction } from '../../navigation';
type variantId = string;
type LineItem = {
  variantId: string;
  quantity: number;
  customAttributes?: any;
  product?: any;
  variant?: any;
};
type ManageCartArg = {
  lineItemsToAdd?: LineItem[];
  lineItemsToRemove?: String[];
  lineItemsToUpdate?: LineItem[];
  showMessage?: boolean;
  openCartOnComplete?: boolean;
  updateCartPageStateRequired?: boolean;
  applyDiscountCode?: string | null;
  removeDiscountCode?: string | null;
};

const useCartActions = (props: any) => {
  const [cartActionLoading, setCartActionLoading] = useState(false);
  const setBlockData = usePageState((store) => store.setBlockData);
  const manageCart = async (params: ManageCartArg) => {
    setCartActionLoading(true);
    const actionData = await handleAction({
      action: 'MANAGE_CART',
      params: {
        lineItemsToAdd: params?.lineItemsToAdd,
        lineItemsToUpdate: params?.lineItemsToUpdate,
        ...(params?.showMessage && { showMessage: params?.showMessage }),
        lineItemsToRemove: params?.lineItemsToRemove,
        applyDiscountCode: params?.applyDiscountCode,
        removeDiscountCode: params?.removeDiscountCode,
      },
    });
    setCartActionLoading(false);
    if (params?.openCartOnComplete) {
      const openCartAction = NavigationAction?.openCart();
      handleAction(openCartAction);
    }
    if (actionData?.cartResponse && params?.updateCartPageStateRequired) {
      setBlockData(actionData?.cartResponse);
    }
  };

  return {
    manageCart,
    cartActionLoading,
  };
};

export { useCartActions };
