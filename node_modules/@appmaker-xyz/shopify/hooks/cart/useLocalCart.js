import { useMemo } from 'react';
import { useAppStorage } from '@appmaker-xyz/core';
import { isCartApiEnabled, shopifyCartToLegacyCart } from '../../helper/helper';
export const useLocalCart = (props) => {
  const cartKey = isCartApiEnabled() ? 'shopifyCart' : 'checkout';
  const rawCart = useAppStorage((state) => state?.[cartKey]);
  const cart = useMemo(() => rawCart ? shopifyCartToLegacyCart(rawCart) : null, [rawCart]);
  return {
    cart,
  };
};
