import React, { useState, useEffect } from 'react';
import {
  useAppStorage,
  usePageState,
  runDataSource,
  applyFilters,
} from '@appmaker-xyz/core';
import { useImmer } from 'use-immer';
import { countryState } from '../../data/country/stateList';
import { getShipsToCountries } from '../../data/country/countries';
import * as yup from 'yup';
import { useFocusEffect } from '@react-navigation/native';

const useAddress = (props) => {
  const { onAction } = props;
  const [addressList, setAddressList] = useState();
  const [defaultAddressId, setDefaultAddressId] = useState();
  const [addressSelectLoading, setAddressSelectLoading] = useState(false);
  const setPageStateVar = usePageState((state) => state.setPageStateVar);
  const [addressLoading, setAddressLoading] = useState(false);
  const refetchCount = usePageState((state) => state.refetchCount || 0);
  function setAddressModalVisible(_visible) {
    setPageStateVar('isAddressModalVisible', _visible);
  }
  const isAddressModalVisible = usePageState(
    (state) => state.isAddressModalVisible,
  );
  const toggleAddressModal = () => {
    setAddressModalVisible(!isAddressModalVisible);
  };
  function openAddressModal() {
    setAddressModalVisible(true);
  }
  function closeAddressModal() {
    setAddressModalVisible(false);
  }
  const user = useAppStorage((state) => state.user);
  const getUserAddress = async () => {
    setAddressLoading(true);
    const [response] = await runDataSource(
      {
        dataSource: {
          source: 'shopify',
          attributes: {},
        },
      },
      {
        methodName: 'customerAddressList',
        params: user,
      },
    );
    setAddressLoading(false);
    if (response?.data?.data?.customer?.addresses?.edges?.length > 0) {
      setAddressList(response?.data?.data?.customer?.addresses?.edges);
    }
    if (response?.data?.data?.customer?.defaultAddress?.id) {
      setDefaultAddressId(response?.data?.data?.customer?.defaultAddress?.id);
    }
  };
  useFocusEffect(
    React.useCallback(() => {
      if (!addressLoading) {
        getUserAddress();
      }
    }, []),
  );
  useEffect(() => {
    getUserAddress();
  }, [refetchCount]);

  const getAddressById = (id) => {
    return addressList?.filter((item) => item?.node?.id === id)[0];
  };
  const getFormattedAddressList = () => {
    let formattedAddressList = addressList?.map?.((address) => {
      return {
        firstName: address?.node?.firstName,
        lastName: address?.node?.lastName,
        id: address?.node?.id,
        formatted: address?.node?.formatted,
      };
    });
    return formattedAddressList;
  };
  const selectAddress = async (
    selectedAddress,
    { setDefault = false, skipSettingBlockData = false },
  ) => {
    const selectedUserAddress = getAddressById(selectedAddress?.id);
    if (onAction) {
      setAddressSelectLoading(true);
      await onAction({
        action: 'SET_SHIPPING_ADDRESS_V2',
        params: {
          setDefault,
          skipSettingBlockData,
          address: selectedUserAddress?.node
            ? formatMailingAddressInput(selectedUserAddress?.node)
            : selectedUserAddress,
        },
      });
      await getUserAddress();
      setAddressSelectLoading(false);
    }
  };

  const addNewAddress = (address) => {
    onAction &&
      onAction({
        action: 'SET_GUEST_ADDRESS_V2',
        params: {
          address,
        },
      });
  };

  const openAddAddress = () => {
    onAction({
      action: 'OPEN_INAPP_PAGE',
      pageId: 'addressFields',
    });
  };
  return {
    addressList,
    user,
    selectAddress,
    addNewAddress,
    formattedAddressList: getFormattedAddressList(),
    addressSelectLoading,
    defaultAddressID: defaultAddressId,
    openAddAddress,
    isAddressModalVisible,
    toggleAddressModal,
    setAddressModalVisible,
    openAddressModal,
    closeAddressModal,
    isAddressLoading: addressLoading,
  };
};

const formatMailingAddressInput = (address) => {
  if (address) {
    return {
      ...(address?.id && { id: address?.id }),
      ...(address?.email && { email: address?.email }),
      address1: address?.address1,
      address2: address?.address2,
      city: address?.city,
      company: address?.company,
      country: address?.country,
      firstName: address?.firstName,
      lastName: address?.lastName,
      phone: address?.phone,
      province: address?.province,
      zip: address?.zip,
      countryCode: address?.countryCode || address?.countryCodeV2,
      provinceCode: address?.provinceCode || address?.provinceCodeV2,
    };
  }
  return address;
};

const useAddressItem = (props) => {
  const address = props?.address?.node ? props?.address?.node : props?.address;
  const onAction = props?.onAction;
  const defaultAddressID = props?.defaultAddressID;
  const isDefault =
    address && defaultAddressID ? address?.id === defaultAddressID : false;
  const [addressSelectLoading, setAddressSelectLoading] = useState(false);
  const [addressDeleteLoading, setAddressDeleteLoading] = useState(false);

  const user = useAppStorage((state) => state.user);
  const setPageStateVar = usePageState((state) => state.setPageStateVar);
  const refetchCount = usePageState((state) => state.refetchCount || 0);
  const deleteAddress = async () => {
    setAddressDeleteLoading(true);
    const [response] = await runDataSource(
      {
        dataSource: {
          source: 'shopify',
          attributes: {},
        },
      },
      {
        methodName: 'customerAddressDelete',
        params: { accessToken: user?.accessToken, id: address?.id },
      },
    );
    setPageStateVar('refetchCount', refetchCount ? refetchCount + 1 : 1);
    setAddressDeleteLoading(false);
    return response?.data?.data.customerAddressDelete;
  };

  const selectAddress = async ({ setDefault, skipSettingBlockData }) => {
    // const selectedUserAddress = getAddressById(selectedAddress?.id);
    if (onAction) {
      setAddressSelectLoading(true);
      await onAction({
        action: 'SET_SHIPPING_ADDRESS_V2',
        params: {
          setDefault,
          skipSettingBlockData,
          address: {
            ...(address?.id && { id: address?.id }),
            ...(address?.email && { email: address?.email }),
            address1: address?.address1,
            address2: address?.address2,
            city: address?.city,
            company: address?.company,
            country: address?.country,
            firstName: address?.firstName,
            lastName: address?.lastName,
            phone: address?.phone,
            province: address?.province,
            zip: address?.zip,
          },
        },
      });
      setPageStateVar('refetchCount', refetchCount ? refetchCount + 1 : 1);
      setAddressSelectLoading(false);
    }
  };

  const editAddress = () => {
    if (onAction) {
      onAction({
        action: 'OPEN_INAPP_PAGE',
        pageId: 'addressFields',
        params: {
          pageData: {
            address,
          },
        },
      });
    }
  };

  return {
    addressDeleteLoading,
    deleteAddress,
    address,
    selectAddress,
    addressSelectLoading,
    isDefault,
    editAddress,
  };
};

const validateShippingAddress = async ({ address, onAction }) => {
  // Check if the country has provinces
  const hasProvinces =
    address?.country && countryState?.[address?.country]?.provinces?.length > 0;

  let addressValidatorShape = {
    email: yup.string().email().required('Enter an Email'),
    address1: yup.string().required('Enter an address'),
    address2: yup.mixed().nullable(),
    city: yup.string().required('Enter a City'),
    // company: yup.string().default(''),
    country: yup
      .mixed()
      .test('required', 'Select a country', (value) => !!value),
    firstName: yup.string().required('Enter a first name'),
    lastName: yup.string().required('Enter a last name'),
    phone: yup
      .mixed()
      .test(
        'required',
        'Please enter a valid mobile number',
        (value) => !!value,
      ),
    zip: yup.mixed().test('required', 'Enter a PIN code', (value) => !!value),
  };

  // Add province validation only if the country has provinces
  if (hasProvinces) {
    addressValidatorShape.province = yup
      .mixed()
      .test('required', 'Select a Province', (value) => !!value);
  } else {
    addressValidatorShape.province = yup.mixed().optional();
  }

  addressValidatorShape = applyFilters(
    'shipping-address-validator',
    addressValidatorShape,
    { yup },
  );
  const addressValidator = yup.object().shape(addressValidatorShape);
  try {
    const resp = await addressValidator.validateSync(address, {
      abortEarly: false,
    });
    return true;
  } catch (error) {
    onAction &&
      onAction({
        action: 'SHOW_MESSAGE',
        params: { title: error.errors.join(' \n') },
      });
    return false;
  }
};

const initialValue = {
  email: '',
  address1: '',
  address2: '',
  city: '',
  company: '',
  country: '',
  firstName: '',
  lastName: '',
  phone: '',
  province: '',
  zip: '',
};

const useAddAddress = ({ onAction, address }) => {
  let editAddress = { ...address } || {};
  if (editAddress.formatted) {
    delete editAddress.formatted;
    editAddress.province = editAddress.provinceCode;
    editAddress.country = editAddress.countryCodeV2;
    delete editAddress.provinceCode;
    delete editAddress.countryCode;
    delete editAddress.countryCodeV2;
  }
  const [isLoading, setIsLoading] = useState(false);
  const user = useAppStorage((state) => state.user);
  let email = user?.email || '';
  let phone = user?.phone || '';
  let firstName = user?.firstName || '';
  let lastName = user?.lastName || '';

  const initialState = !address
    ? {
        ...initialValue,
        ...(email && { email: email }),
        ...(phone && { phone: phone }),
        ...(firstName && { firstName: firstName }),
        ...(lastName && { lastName: lastName }),
      }
    : {
        ...initialValue,
        ...(email && { email: email }),
        ...(phone && { phone: phone }),
        ...(firstName && { firstName: firstName }),
        ...(lastName && { lastName: lastName }),
        ...editAddress,
      };
  const [formData, updateFormData] = useImmer(initialState);
  const isEditAddress = !!formData?.id;
  const updateForm = (key, value) => {
    updateFormData((draft) => {
      draft[key] = value;
    });
  };
  const getStateList = () => {
    return formData?.country && countryState?.[formData?.country]?.provinces
      ? countryState[formData.country].provinces
      : [];
  };

  const addNewAddress = async (address, { isDefault = false } = {}) => {
    setIsLoading(true);
    const isValid = await validateShippingAddress({ address, onAction });
    if (onAction && isValid) {
      await onAction({
        action: isEditAddress ? 'UPDATE_ADDRESS_V2' : 'SET_GUEST_ADDRESS_V2',
        params: {
          setDefault: isDefault,
          address: {
            ...(formData?.id && { id: formData?.id }),
            ...(formData?.email && { email: formData?.email }),
            address1: address?.address1,
            address2: address?.address2,
            city: address?.city,
            company: address?.company,
            country: address?.country,
            firstName: address?.firstName,
            lastName: address?.lastName,
            phone: address?.phone,
            province: address?.province,
            zip: address?.zip,
          },
        },
      });
    }
    setIsLoading(false);
  };

  return {
    isEditAddress,
    formData,
    updateFormData,
    updateForm,
    statesList: getStateList(),
    countriesListFn: getShipsToCountries,
    addNewAddress,
    isAddAddressLoading: isLoading,
    ...formData,
  };
};

export { useAddress, useAddressItem, useAddAddress };
