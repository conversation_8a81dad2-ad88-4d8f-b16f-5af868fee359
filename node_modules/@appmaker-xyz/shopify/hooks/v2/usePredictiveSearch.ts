import { useState, useEffect } from 'react';
import { useDataSourceV2 } from '@appmaker-xyz/core';
import debounce from 'lodash/debounce';
import { usePageState } from '@appmaker-xyz/core';

type Query = {
  text: string;
  trackingParameters: string;
};

type Collection = {
  handle: string;
  trackingParameters: string;
};

type Product = {
  handle: string;
  trackingParameters: string;
};

type PredictiveSearchResponse = {
  queries: Query[];
  collections: Collection[];
  products: Product[];
};

export function usePredictiveSearch(searchTerm: string): { results: PredictiveSearchResponse; isLoading: boolean } {
  const [debouncedTerm, setDebouncedTerm] = useState<string>(searchTerm);
  const setPageState = usePageState((state) => state.setPageState);
  const [{ isLoading }, { item }] = useDataSourceV2({
    dataSource: {
      source: 'shopify', // Adjust as needed
      methodName: 'predictiveSearch',
      dataExtractor: (response: any) => response?.data?.data?.predictiveSearch as PredictiveSearchResponse, // Adjust based on your API response
      params: {
        query: debouncedTerm,
      },
    },
    enabled: !!debouncedTerm && debouncedTerm.trim() !== '', // Check if debouncedTerm is not only truthy but also not an empty string
  });
  useEffect(() => {
    setPageState({ predictiveResults: item });
  }, [item])
  useEffect(() => {
    const debouncer = debounce((term: string) => setDebouncedTerm(term), 300); // 300ms debounce time
    debouncer(searchTerm);

    return () => {
      debouncer.cancel();
    };
  }, [searchTerm]);

  return {
    results: item as PredictiveSearchResponse,
    isLoading: isLoading && !!debouncedTerm.trim(), // Set isLoading to true only when there is a valid search term
  };
}

export default usePredictiveSearch;
