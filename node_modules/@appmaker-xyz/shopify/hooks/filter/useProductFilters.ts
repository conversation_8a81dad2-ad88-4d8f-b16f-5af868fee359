import { useState } from 'react';
import { useDataSourceV2 } from '@appmaker-xyz/core';
import { ShopifyDataSourceConfig } from '../../datasource/shopify';

type QueryInputSearch = {
  searchQuery: string;
};
type QueryInputCollectionId = {
  collectionId: string;
};
type QueryInputCollectionHandle = {
  collectionHandle: string;
};

// If extra types are added here make sure to add the check in below dataSource `enable` key
type QueryInput =
  | QueryInputCollectionId
  | QueryInputCollectionHandle
  | QueryInputSearch;

type SelectedFilterItem = {
  count: number;
  id: string;
  input: string;
  label: string;
};

type SelectedFilters = {
  [filterKey: string]: {
    [filterValueKey: string]: SelectedFilterItem;
  };
};
type ProductFilterProps = {
  query: QueryInput;
  preSelectedFilters?: SelectedFilters;
};

// TODO: return types
const useProductFilters = ({
  query,
  preSelectedFilters = null,
}: ProductFilterProps) => {
  const [selectedFilters, setSelectedFilters] =
    useState<SelectedFilters>(preSelectedFilters);
  const dataSource = ShopifyDataSourceConfig.ProductFiltersNext({
    ...('collectionId' in query && { collectionId: query.collectionId }),
    ...('collectionHandle' in query && {
      collectionHandle: query.collectionHandle,
    }),
    ...('searchQuery' in query && { searchQuery: query.searchQuery }),
    ...(selectedFilters && { selectedFilters }),
  });

  const [queryResp, { item }] = useDataSourceV2({
    dataSource,
    enabled:
      'collectionId' in query ||
      'collectionHandle' in query ||
      'searchQuery' in query,
  });
  const setSelectedProductFilters = (filters: SelectedFilters) => {
    setSelectedFilters(filters);
  };
  const getSingleFilter = ({ filterKey }) => {
    return item?.find((item) => item?.id === filterKey);
  };
  return {
    filters: item || [],
    setSelectedFilters: setSelectedProductFilters,
    getSingleFilter,
    ...queryResp,
  };
};

export { useProductFilters };
