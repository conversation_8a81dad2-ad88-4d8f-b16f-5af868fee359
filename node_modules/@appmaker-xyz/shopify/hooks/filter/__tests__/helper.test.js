import { converToShopifyParams } from '../helper';

describe('converToShopifyParams', () => {
  test('should first', () => {
    const input = {
      selectedFilters: {
        'filter.p.m.my_fields.material': {
          'filter.p.m.my_fields.material.art-silk': {
            count: 283,
            id: 'filter.p.m.my_fields.material.art-silk',
            input:
              '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Art Silk"}}',
            label: 'Art Silk',
          },
          'filter.p.m.my_fields.material.banarasi-silk': {
            count: 36,
            id: 'filter.p.m.my_fields.material.banarasi-silk',
            input:
              '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Banarasi Silk"}}',
            label: 'Banarasi Silk',
          },
        },
        'filter.p.m.my_fields.occasion': {
          'filter.p.m.my_fields.occasion.casual': {
            count: 435,
            id: 'filter.p.m.my_fields.occasion.casual',
            input:
              '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Casual"}}',
            label: 'Casual',
          },
        },
      },
      availableFilters: [
        {
          id: 'filter.v.availability',
          label: 'Availability',
          type: 'LIST',
          values: [
            {
              count: 1000,
              id: 'filter.v.availability.1',
              input: '{"available":true}',
              label: 'In stock',
            },
            {
              count: 0,
              id: 'filter.v.availability.0',
              input: '{"available":false}',
              label: 'Out of stock',
            },
          ],
        },
        {
          id: 'filter.v.price',
          label: 'Price',
          type: 'PRICE_RANGE',
          values: [
            {
              count: 0,
              id: 'filter.v.price',
              input: '{"price":{"min":0,"max":12049.0}}',
              label: 'Price',
            },
          ],
        },
        {
          id: 'filter.p.product_type',
          label: 'Product type',
          type: 'LIST',
          values: [
            {
              count: 1000,
              id: 'filter.p.product_type.saree',
              input: '{"productType":"Saree"}',
              label: 'Saree',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.material',
          label: 'Material',
          type: 'LIST',
          values: [
            {
              count: 280,
              id: 'filter.p.m.my_fields.material.art-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Art Silk"}}',
              label: 'Art Silk',
            },
            {
              count: 36,
              id: 'filter.p.m.my_fields.material.banarasi-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Banarasi Silk"}}',
              label: 'Banarasi Silk',
            },
            {
              count: 5,
              id: 'filter.p.m.my_fields.material.bhagalpuri-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Bhagalpuri Silk"}}',
              label: 'Bhagalpuri Silk',
            },
            {
              count: 28,
              id: 'filter.p.m.my_fields.material.brasso',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Brasso"}}',
              label: 'Brasso',
            },
            {
              count: 14,
              id: 'filter.p.m.my_fields.material.chanderi',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Chanderi"}}',
              label: 'Chanderi',
            },
            {
              count: 87,
              id: 'filter.p.m.my_fields.material.chiffon',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Chiffon"}}',
              label: 'Chiffon',
            },
            {
              count: 75,
              id: 'filter.p.m.my_fields.material.cotton-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Cotton Silk"}}',
              label: 'Cotton Silk',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.material.crepe',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Crepe"}}',
              label: 'Crepe',
            },
            {
              count: 19,
              id: 'filter.p.m.my_fields.material.dola-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Dola Silk"}}',
              label: 'Dola Silk',
            },
            {
              count: 132,
              id: 'filter.p.m.my_fields.material.georgette',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Georgette"}}',
              label: 'Georgette',
            },
            {
              count: 7,
              id: 'filter.p.m.my_fields.material.jacquard',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Jacquard"}}',
              label: 'Jacquard',
            },
            {
              count: 8,
              id: 'filter.p.m.my_fields.material.jute',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Jute"}}',
              label: 'Jute',
            },
            {
              count: 3,
              id: 'filter.p.m.my_fields.material.kanjivaram-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Kanjivaram Silk"}}',
              label: 'Kanjivaram Silk',
            },
            {
              count: 2,
              id: 'filter.p.m.my_fields.material.kota-doria',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Kota Doria"}}',
              label: 'Kota Doria',
            },
            {
              count: 91,
              id: 'filter.p.m.my_fields.material.linen',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Linen"}}',
              label: 'Linen',
            },
            {
              count: 5,
              id: 'filter.p.m.my_fields.material.manipuri-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Manipuri Silk"}}',
              label: 'Manipuri Silk',
            },
            {
              count: 2,
              id: 'filter.p.m.my_fields.material.mysore-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Mysore Silk"}}',
              label: 'Mysore Silk',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.material.net',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Net"}}',
              label: 'Net',
            },
            {
              count: 34,
              id: 'filter.p.m.my_fields.material.organza',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Organza"}}',
              label: 'Organza',
            },
            {
              count: 5,
              id: 'filter.p.m.my_fields.material.poly-cotton',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Poly Cotton"}}',
              label: 'Poly Cotton',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.material.pure-chiffon',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Pure Chiffon"}}',
              label: 'Pure Chiffon',
            },
            {
              count: 113,
              id: 'filter.p.m.my_fields.material.pure-cotton',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Pure Cotton"}}',
              label: 'Pure Cotton',
            },
            {
              count: 11,
              id: 'filter.p.m.my_fields.material.pure-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Pure Silk"}}',
              label: 'Pure Silk',
            },
            {
              count: 24,
              id: 'filter.p.m.my_fields.material.satin',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Satin"}}',
              label: 'Satin',
            },
            {
              count: 3,
              id: 'filter.p.m.my_fields.material.tussar-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Tussar Silk"}}',
              label: 'Tussar Silk',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.material.vichitra-silk',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"material","value":"Vichitra Silk"}}',
              label: 'Vichitra Silk',
            },
          ],
        },
        {
          id: 'filter.v.option.size',
          label: 'Size',
          type: 'LIST',
          values: [
            {
              count: 1,
              id: 'filter.v.option.size.semi-stitched',
              input:
                '{"variantOption":{"name":"size","value":"SEMI STITCHED"}}',
              label: 'SEMI STITCHED',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.occasion',
          label: 'Occasion',
          type: 'LIST',
          values: [
            {
              count: 438,
              id: 'filter.p.m.my_fields.occasion.casual',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Casual"}}',
              label: 'Casual',
            },
            {
              count: 438,
              id: 'filter.p.m.my_fields.occasion.daily',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Daily"}}',
              label: 'Daily',
            },
            {
              count: 498,
              id: 'filter.p.m.my_fields.occasion.festive',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Festive"}}',
              label: 'Festive',
            },
            {
              count: 3,
              id: 'filter.p.m.my_fields.occasion.haldi',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Haldi"}}',
              label: 'Haldi',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.occasion.mehendi',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Mehendi"}}',
              label: 'Mehendi',
            },
            {
              count: 19,
              id: 'filter.p.m.my_fields.occasion.office-wear',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Office wear"}}',
              label: 'Office wear',
            },
            {
              count: 60,
              id: 'filter.p.m.my_fields.occasion.party',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Party"}}',
              label: 'Party',
            },
            {
              count: 1,
              id: 'filter.p.m.my_fields.occasion.reception',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Reception"}}',
              label: 'Reception',
            },
            {
              count: 9,
              id: 'filter.p.m.my_fields.occasion.sangeet',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"occasion","value":"Sangeet"}}',
              label: 'Sangeet',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.print_pattern',
          label: 'Print / Pattern',
          type: 'LIST',
          values: [
            {
              count: 18,
              id: 'filter.p.m.my_fields.print_pattern.abstract-print',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Abstract Print"}}',
              label: 'Abstract Print',
            },
            {
              count: 26,
              id: 'filter.p.m.my_fields.print_pattern.bandhani',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Bandhani"}}',
              label: 'Bandhani',
            },
            {
              count: 28,
              id: 'filter.p.m.my_fields.print_pattern.checked',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Checked"}}',
              label: 'Checked',
            },
            {
              count: 100,
              id: 'filter.p.m.my_fields.print_pattern.digital-print',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Digital Print"}}',
              label: 'Digital Print',
            },
            {
              count: 349,
              id: 'filter.p.m.my_fields.print_pattern.floral',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Floral"}}',
              label: 'Floral',
            },
            {
              count: 35,
              id: 'filter.p.m.my_fields.print_pattern.foil-print',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Foil Print"}}',
              label: 'Foil Print',
            },
            {
              count: 63,
              id: 'filter.p.m.my_fields.print_pattern.geometric',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Geometric"}}',
              label: 'Geometric',
            },
            {
              count: 7,
              id: 'filter.p.m.my_fields.print_pattern.gota-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Gota Work"}}',
              label: 'Gota Work',
            },
            {
              count: 37,
              id: 'filter.p.m.my_fields.print_pattern.ikkat-print',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Ikkat Print"}}',
              label: 'Ikkat Print',
            },
            {
              count: 36,
              id: 'filter.p.m.my_fields.print_pattern.kalamkari',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Kalamkari"}}',
              label: 'Kalamkari',
            },
            {
              count: 8,
              id: 'filter.p.m.my_fields.print_pattern.mirror-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Mirror Work"}}',
              label: 'Mirror Work',
            },
            {
              count: 15,
              id: 'filter.p.m.my_fields.print_pattern.paisley',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Paisley"}}',
              label: 'Paisley',
            },
            {
              count: 57,
              id: 'filter.p.m.my_fields.print_pattern.sequins-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Sequins Work"}}',
              label: 'Sequins Work',
            },
            {
              count: 32,
              id: 'filter.p.m.my_fields.print_pattern.solid',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Solid"}}',
              label: 'Solid',
            },
            {
              count: 1,
              id: 'filter.p.m.my_fields.print_pattern.solid',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Solid "}}',
              label: 'Solid ',
            },
            {
              count: 47,
              id: 'filter.p.m.my_fields.print_pattern.striped',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Striped"}}',
              label: 'Striped',
            },
            {
              count: 45,
              id: 'filter.p.m.my_fields.print_pattern.thread-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Thread Work"}}',
              label: 'Thread Work',
            },
            {
              count: 4,
              id: 'filter.p.m.my_fields.print_pattern.zardosi-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Zardosi Work"}}',
              label: 'Zardosi Work',
            },
            {
              count: 360,
              id: 'filter.p.m.my_fields.print_pattern.zari-work',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"print_pattern","value":"Zari Work"}}',
              label: 'Zari Work',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.stitch_type',
          label: 'Stitch Type',
          type: 'LIST',
          values: [
            {
              count: 5,
              id: 'filter.p.m.my_fields.stitch_type.stitched',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"stitch_type","value":"Stitched"}}',
              label: 'Stitched',
            },
            {
              count: 995,
              id: 'filter.p.m.my_fields.stitch_type.unstitched',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"stitch_type","value":"Unstitched"}}',
              label: 'Unstitched',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.style',
          label: 'Style',
          type: 'LIST',
          values: [
            {
              count: 5,
              id: 'filter.p.m.my_fields.style.paithani',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"style","value":"Paithani"}}',
              label: 'Paithani',
            },
            {
              count: 20,
              id: 'filter.p.m.my_fields.style.patola',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"style","value":"Patola"}}',
              label: 'Patola',
            },
          ],
        },
        {
          id: 'filter.p.m.my_fields.work',
          label: 'Work',
          type: 'LIST',
          values: [
            {
              count: 84,
              id: 'filter.p.m.my_fields.work.embroidered',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"work","value":"Embroidered"}}',
              label: 'Embroidered',
            },
            {
              count: 352,
              id: 'filter.p.m.my_fields.work.handloom-woven',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"work","value":"Handloom \\/ Woven"}}',
              label: 'Handloom / Woven',
            },
            {
              count: 563,
              id: 'filter.p.m.my_fields.work.printed',
              input:
                '{"productMetafield":{"namespace":"my_fields","key":"work","value":"Printed"}}',
              label: 'Printed',
            },
          ],
        },
      ],
    };

    const filterFormat = converToShopifyParams(input.selectedFilters);
    console.log(filterFormat, '1223');
  });
});
