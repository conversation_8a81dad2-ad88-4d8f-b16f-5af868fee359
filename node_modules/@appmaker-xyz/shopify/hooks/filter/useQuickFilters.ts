import { usePageState } from '@appmaker-xyz/core';
import { useProductFilters } from './useProductFilters';
import { useProductFilterActions } from '../useProductFilterActions';
type QueryInputCollectionId = {
  collectionId: string;
};
type QueryInputCollectionHandle = {
  collectionHandle: string;
};
type QueryInputSearch = {
  searchQuery: string;
};
type CurrentActionParams =
  | QueryInputCollectionId
  | QueryInputCollectionHandle
  | QueryInputSearch;

type CurrentAction = {
  action: string;
  params: CurrentActionParams;
};

type FilterID = string;
type FilterValueID = string;
interface FilterValue {
  count?: number;
  id: FilterValueID;
  input: string;
  label: string;
}

type FilterType = 'LIST' | 'PRICE_RANGE';

interface Filter {
  id: FilterID;
  label: string;
  type: FilterType;
  values: FilterValue[];
}

type Filters = Filter[] | [];
type QuickFilterSetProps = {
  filterKey: string;
  filterItem: FilterValue;
};
type QuickFilterRemoveProps = {
  filterKey: string;
  filterItem: FilterValue;
};
type SelectedFilters = {
  [filterKey: FilterID]: {
    [filterValueKey: FilterValueID]: FilterValue;
  };
};
interface UseQuickFiltersReturn {
  filters: Filters | null;
  isFilterLoading: boolean;
  applyQuickFilter: (filter: QuickFilterSetProps) => void;
  clearAllFilters: () => void;
  removeQuickFilter: (filter: QuickFilterRemoveProps) => void;
  getSingleFilter: (filter: { filterKey: string }) => Filter | null;
  isFilterAvailable: boolean;
  selectedFilters: SelectedFilters | null;
}
const useQuickFilters = (): UseQuickFiltersReturn => {
  const actionParams: CurrentAction = usePageState(
    (state) => state?.currentAction,
  );
  
  const { setFilter, clearAllFilters, removeFilter, currentFilters } =
    useProductFilterActions();
  const { filters, setSelectedFilters, getSingleFilter, ...filterQueryResp } =
    useProductFilters({
      query: actionParams?.params,
      preSelectedFilters: currentFilters
    });
  const applyQuickFilter = ({ filterKey, filterItem }) =>
    setFilter({
      filterKey,
      filterValueID: filterItem?.id,
      filterValue: filterItem,
    });
  const removeQuickFilter = ({ filterKey, filterItem }) =>
    removeFilter({ filterKey, filterValueID: filterItem?.id });
  const isFilterAvailable = filterQueryResp?.isLoading === false && filters?.length > 0;
  return {
    filters, 
    isFilterLoading: filterQueryResp?.isLoading,
    applyQuickFilter,
    clearAllFilters,
    removeQuickFilter,
    getSingleFilter,
    isFilterAvailable,
    selectedFilters: currentFilters,
  };
};

export { useQuickFilters };
