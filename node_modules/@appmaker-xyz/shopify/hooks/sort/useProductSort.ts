import { useDataSourceV2 } from '@appmaker-xyz/core';
import { ShopifyDataSourceConfig } from '../../datasource/shopify';

type QueryInputSearch = {
  searchQuery?: string;
};
type QueryInput = QueryInputSearch;

const useProductSort = (props?: QueryInput) => {
const dataSource = ShopifyDataSourceConfig.ProductSort({
    ...(props?.searchQuery && { search: props?.searchQuery }),
  });
  const [queryResp, { item }] = useDataSourceV2({
    dataSource,
    enabled: true,
  });
  return {
    sort: item,
    ...queryResp,
  };
};

export { useProductSort };
