import { useState, useEffect } from 'react';
import { usePageState } from '@appmaker-xyz/core';
import { useProducts } from '../product/useProducts';
import { useProductFilters } from '../filter/useProductFilters';
import { useProductSort } from '../sort/useProductSort';

type filters = { [key: string]: any }; // Replace 'any' with more specific type if possible
type sort = { [key: string]: any }; // Replace 'any' with more specific type if possible
type limit = number; // not required number;
type surface = string; // not required string;

type ProductListProps = {
  filters?: filters;
  sort?: sort;
  limit?: limit;
  surface?: surface;
};

type CurrentAction = {
  action: string;
  params: CurrentActionParams;
};
type QueryInputCollectionId = {
  collectionId: string;
};
type QueryInputCollectionHandle = {
  collectionHandle: string;
};
type QueryInputSearch = {
  searchQuery: string;
};
type CurrentActionParams = QueryInputCollectionId | QueryInputCollectionHandle | QueryInputSearch;

/**
 * Only use in Collection Page.  Dependant on currentAction from pageState
 * @param props
 * @returns
 */
const useProductList = (props: ProductListProps) => {
  const actionParams: CurrentAction = usePageState(
    (state) => state?.currentAction,
  );
  const currentActionParams: CurrentActionParams = actionParams?.params;
  const { productList, ...productQueryResp } = useProducts({
    query: currentActionParams,
    ...(props?.filters && { filters: props?.filters }),
    ...(props?.sort && { sort: props?.sort }),
    ...(props?.limit && { limit: props?.limit }),
    ...(props?.surface && { surface: props?.surface }),
  });
  const { filters, setSelectedFilters, ...filterQueryResp } = useProductFilters(
    {
      query: actionParams?.params,
      ...(props?.filters && { preSelectedFilters: props?.filters }),
    },
  );
  const { sort, ...sortQueryResp } = useProductSort({
    ...('searchQuery' in currentActionParams &&
      currentActionParams?.searchQuery && {
        searchQuery: currentActionParams?.searchQuery,
      }),
  });
  return {
    products: {
      items: productList,
      ...productQueryResp,
    },
    filters: {
      items: filters,
      setSelectedFilters,
      ...filterQueryResp,
    },
    sort: {
      items: sort,
      ...sortQueryResp,
    },
  };
};

export { useProductList };
