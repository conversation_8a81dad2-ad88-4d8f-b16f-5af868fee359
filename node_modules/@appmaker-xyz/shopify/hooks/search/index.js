import { usePageState } from '@appmaker-xyz/core';
import { isEmpty, trim } from 'lodash';
import { useEffect } from 'react';
async function getSuggestions(query) {
  const res = await fetch(
    `https://services.mybcapps.com/bc-sf-filter/search/suggest?q=${query}&shop=the-box-peru-sac.myshopify.com&t=1671518586343&product_available=true&suggestion_limit=10&collection_limit=3&product_limit=30&page_limit=3&dym_limit=2&event_type=suggest`,
  );
  const data = await res.json();
  // console.log(data,'data');
  const suggestResponse = {
    suggestions: [],
    products: [],
  };
  const { suggestions, products } = data;
  suggestResponse.suggestions = suggestions.map((suggestion) => {
    return {
      label: suggestion,
      query: suggestion,
    };
  });

  suggestResponse.products = products.map((product) => {
    return {
      label: product.title,
      query: product.title,
    };
  });
  return suggestResponse;
}
export function useSearchResult() {
  const query = usePageState((state) => state.searchQuery);

  const suggestionsResponse = usePageState(
    (state) => state.suggestionsResponse,
  );
  return { suggestionsResponse, query };
}
export function useSearch() {
  const query = usePageState((state) => state.searchQuery);
  const suggestLoading = usePageState((state) => state.suggestLoading);

  const setPageStateVar = usePageState((state) => state.setPageStateVar);
  const setPageState = usePageState((state) => state.setPageState);
  const searchResultCount = usePageState(
    (state) =>
      state?.dataSourceResponse?.productList?.data?.data?.products?.totalCount,
  );
  useEffect(() => {
    // if (!isEmpty(trim(query))) {
    //   setPageStateVar('suggestLoading', true);
    //   getSuggestions(query)
    //     .then((suggestionsResponse) => {
    //       setPageState({
    //         suggestLoading: false,
    //         suggestionsResponse,
    //       });
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //       setPageState({
    //         suggestLoading: false,
    //       });
    //     });
    // }
  }, [query]);
  function setQuery(_query) {
    // setPageStateVar('searchQuery', _query);
    setPageStateVar('searchQuery', _query);
  }
  return { query, suggestLoading, setQuery, searchResultCount };
}

export function useSearchSuggest(props) {
  let terms = [];
  const blockData = props?.blockData;
  if (blockData?.data?.terms?.items?.length > 0) {
    terms = blockData?.data?.terms;
  }
  return { data: blockData, terms };
}
