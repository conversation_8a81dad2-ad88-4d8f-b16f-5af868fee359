import { useState } from 'react';
import { currencyHelper } from '../../helper';
import { appStorageApi, runDataSource } from '@appmaker-xyz/core';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const schema = z.object({
  firstName: z.string().nonempty('First name is required'),
  lastName: z.string().nonempty('Last name is required'),
  email: z.string().email('Invalid email address'),
});

export function useUserProfileV2(props) {
  const { attributes, onAction, pageDispatch, coreDispatch, defaultValues } =
    props;
  const user = appStorageApi().getState()?.user;

  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, formState, setError, setFocus } = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
    },
  });

  const shopifyDatasource = {
    dataSource: {
      source: 'shopify',
      attributes: {},
    },
  };
  const updateProfile = handleSubmit(async (data) => {
    setIsLoading(true);
    try {
      const userData = {
        firstName: data?.firstName,
        lastName: data?.lastName,
        email: data?.email,
        phone: data?.phone,
        accessToken: user?.accessToken,
      };

      const [response] = await runDataSource(shopifyDatasource, {
        methodName: 'customerUpdate',
        params: userData,
      });
      if (response.status === 200) {
        setIsLoading(false);
        const customerUpdateData = response?.data?.data?.customerUpdate;
        if (
          !(customerUpdateData?.customerUserErrors?.length > 0) &&
          customerUpdateData?.customer
        ) {
          coreDispatch({
            type: 'SET_APP_VAR_LOCAL',
            name: 'user',
            // value: response.data.data[].checkout,
            value: {
              accessToken: user?.accessToken,
              ...customerUpdateData?.customer,
            },
          });
          onAction?.({
            action: 'SHOW_MESSAGE',
            params: {
              title: 'Profile updated successfully',
            },
          });

          onAction?.({
            action: 'GO_BACK',
          });
          return { status: 'success', data: response };
        } else if (customerUpdateData?.customerUserErrors?.length > 0) {
          showShopifyErrorMessage(customerUpdateData?.customerUserErrors);
        }
      } else {
        onAction?.({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, please try again.',
          },
        });
        setIsLoading(false);
        return {
          status: 'fail',
          data: response,
          error: true,
          message: 'Something went wrong',
        };
      }
    } catch (e) {
      setIsLoading(false);
      onAction?.({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Something went wrong, please try again.',
        },
      });
      return {
        status: 'fail',
        error: true,
        message: 'Something went wrong',
      };
    }
  });

  // TODO fix this,
  //   const changePassword = async () => {
  //     // vhhgfg
  //     if (newPassword && confirmPassword && newPassword === confirmPassword) {
  //       setIsLoading(true);
  //       try {
  //         const [response] = await runDataSource(shopifyDatasource, {
  //           methodName: 'customerUpdate',
  //           params: { password: newPassword, accessToken: user?.accessToken },
  //         });
  //         setIsLoading(false);
  //         if (response.status === 200) {
  //           setIsLoading(false);
  //           const customerUpdateData = response?.data?.data?.customerUpdate;
  //           if (
  //             !(customerUpdateData?.customerUserErrors?.length > 0) &&
  //             customerUpdateData?.customer
  //           ) {
  //             coreDispatch({
  //               type: 'SET_APP_VAR_LOCAL',
  //               name: 'user',
  //               // value: response.data.data[].checkout,
  //               value: {
  //                 accessToken:
  //                   customerUpdateData?.customerAccessToken?.accessToken ||
  //                   user?.accessToken,
  //                 ...customerUpdateData?.customer,
  //               },
  //             });
  //             onAction?.({
  //               action: 'SHOW_MESSAGE',
  //               params: {
  //                 title: 'Password Changed Successfully',
  //               },
  //             });

  //             onAction?.({
  //               action: 'GO_BACK',
  //             });
  //             return { status: 'success', data: response };
  //           } else if (customerUpdateData?.customerUserErrors?.length > 0) {
  //             showShopifyErrorMessage(customerUpdateData?.customerUserErrors);
  //           }
  //         } else {
  //           setIsLoading(false);
  //           onAction?.({
  //             action: 'SHOW_MESSAGE',
  //             params: {
  //               title: 'Something went wrong please try again',
  //             },
  //           });
  //           return {
  //             status: 'fail',
  //             data: response,
  //             error: true,
  //             message: 'Something went wrong',
  //           };
  //         }
  //       } catch (e) {
  //         setIsLoading(false);
  //       }
  //     } else {
  //       onAction?.({
  //         action: 'SHOW_MESSAGE',
  //         params: {
  //           title: 'Passwords do not match please try again',
  //         },
  //       });
  //       return {
  //         status: 'fail',
  //         // data: response,
  //         error: true,
  //         message: 'Password do not match',
  //       };
  //     }
  //   };

  const showShopifyErrorMessage = (errors) => {
    if (errors?.length > 0) {
      const errorMessage = errors[0]?.message;
      onAction?.({
        action: 'SHOW_MESSAGE',
        params: {
          title: errorMessage,
        },
      });
    }
  };

  return {
    user,
    firstName: user?.firstName,
    lastName: user?.lastName,
    email: user?.email,
    phone: user?.phone,
    updateProfile,
    isLoading,
    control,
    handleSubmit,
    formState,
    setError,
    setFocus,
  };
}
