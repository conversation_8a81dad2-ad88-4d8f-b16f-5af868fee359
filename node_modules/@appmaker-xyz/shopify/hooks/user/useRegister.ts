import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAction } from "@appmaker-xyz/react-native";
const schema = z
    .object({
        first_name: z.string({
            required_error: 'Please enter your first name',
        }),
        last_name: z.string({
            required_error: 'Please enter your last name',
        }),
        email: z.string({
            required_error: 'Please enter your email address',
        })
            .trim()
            .email("Please enter a valid email address"),
        password: z.string({
            required_error: 'Please enter a password',
        }).trim(),
        repeat_password: z.string(
            {
                required_error: 'Please confirm your password',
            }
        ).trim(),
        phone: z.string().optional(),
        agree_terms: z.literal(true, {
            errorMap: () => ({ message: "Please agree to the Terms of Service" }),
        }),
        accepts_marketing: z.boolean().optional(),
    })
    .refine((data) => data.password === data.repeat_password, {
        message: 'Passwords do not match',
        path: ['repeat_password'],
    });
type RegisterFormType = z.infer<typeof schema>;
export function useRegister(
    {
        defaultValues
    }: {
        defaultValues?: Partial<RegisterFormType>
    } = {}
) {
    const onAction = useAction();
    const {
        control,
        handleSubmit,
        formState,
        setError,
        setFocus
    } = useForm({
        resolver: zodResolver(schema),
        defaultValues,
    });
    const [loading, setLoading] = useState(false);
    const submitRegister = handleSubmit(async (data) => {
        setLoading(true);
        const resp = await onAction({
            action: 'REGISTER_USER',
            customFormData: data,
        });
        setError('email', {
            type: 'manual',
            message: 'This email is already registered',
        });
        setLoading(false);
    })
    return {
        control,
        handleSubmit,
        formState,
        submitRegister,
        isLoading: loading,
        setFocus,
    }
}