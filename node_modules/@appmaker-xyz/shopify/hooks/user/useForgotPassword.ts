import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useAction } from "@appmaker-xyz/react-native";
const schema = z
    .object({
        email: z.string({
            required_error: 'Please enter your email address',
        }).trim()
            .email("Please enter a valid email address"),
    });
type FormType = z.infer<typeof schema>;
export function useForgotPassword(
    {
        defaultValues
    }: {
        defaultValues?: Partial<FormType>
    } = {}
) {
    const onAction = useAction();
    const {
        control,
        handleSubmit,
        formState,
        setError,
    } = useForm({
        resolver: zodResolver(schema),
        defaultValues,
    });
    const [loading, setLoading] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const submit = handleSubmit(async (data) => {
        setLoading(true);
        const resp = await onAction({
            action: 'FORGOT_PASSWORD',
            params: data,
        });
        setLoading(false);
        if (resp.status) {
            setIsSuccess(true);
        }
    })
    return {
        control,
        handleSubmit,
        formState,
        submit,
        isLoading: loading,
        isSuccess,
        setIsSuccess
    }
}