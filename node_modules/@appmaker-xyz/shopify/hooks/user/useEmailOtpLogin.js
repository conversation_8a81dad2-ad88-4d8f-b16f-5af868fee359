import { useReducer } from 'react';
import { appmaker } from '@appmaker-xyz/core';

const initialState = {
  currentStep: 'send', // 'send' or 'verify'
  email: null,
  sendStatus: 'idle', // 'loading', 'success', 'error'
  sendErrorMessage: null,
  otpResponse: null,
  otpCode: null,
  verifyStatus: 'idle', // 'loading', 'success', 'error'
  verifyErrorMessage: null,
  verifyResponse: null,
  resendStatus: 'idle', // 'loading', 'success', 'error'
  resendErrorMessage: null,
  canResend: false,
  failedAttemptCount: 0,
  resendCount: 0,
};

const makeErrorToString = (error) => {
  if (typeof error !== 'string') {
    return JSON.stringify(error);
  }
  return error;
};

const reducer = (state = initialState, action) => {
  switch (action.type) {
    case 'RESET':
      return {
        ...state,
        currentStep: 'send',
        email: action.email,
        sendErrorMessage: null,
        verifyErrorMessage: null,
        resendCount: 0,
        failedAttemptCount: 0,
        otpCode: '',
      };
    case 'SEND_CODE':
      return {
        ...state,
        currentStep: 'verify',
        sendStatus: 'loading',
        email: action.email,
        sendErrorMessage: null,
      };
    case 'SEND_CODE_SUCCESS':
      return {
        ...state,
        sendStatus: 'success',
        otpResponse: action.payload,
        canResend: true,
        otpCode: '',
      };
    case 'SEND_CODE_ERROR':
      return {
        ...state,
        sendStatus: 'error',
        sendErrorMessage: action.error,
      };
    case 'VERIFY_CODE':
      return {
        ...state,
        verifyStatus: 'loading',
        verifyErrorMessage: null,
        otpCode: action.otpCode,
      };
    case 'VERIFY_CODE_SUCCESS':
      return {
        ...state,
        currentStep: action.payload?.nextStep || 'done',
        verifyStatus: 'success',
        verifyResponse: action.payload,
        failedAttemptCount: 0,
      };
    case 'VERIFY_CODE_ERROR':
      return {
        ...state,
        verifyStatus: 'error',
        verifyErrorMessage: action.error,
        failedAttemptCount: state.failedAttemptCount + 1,
        otpCode: '',
      };
    case 'RESEND_CODE':
      return {
        ...state,
        resendStatus: 'loading',
        resendErrorMessage: null,
      };
    case 'RESEND_CODE_SUCCESS':
      return {
        ...state,
        resendStatus: 'success',
        otpResponse: action.payload,
        resendCount: state.resendCount + 1,
        otpCode: '',
      };
    case 'RESEND_CODE_ERROR':
      return {
        ...state,
        resendStatus: 'error',
        resendErrorMessage: action.error,
      };
    default:
      return state;
  }
};

export function useEmailOtpLogin({
  handleAction,
  redirectAction,
  ignoreLoginRedirect = false,
  initialData = {},
} = {}) {
  const [state, dispatch] = useReducer(reducer, initialState);
  const otpDigitCount = appmaker.applyFilters(
    'appmaker-email-otp-digit-count',
    4,
  );

  async function sendCode(email) {
    if (email) {
      dispatch({ type: 'SEND_CODE', email });
      try {
        const { data, error } = await appmaker.applyFilters(
          'appmaker-send-email-otp',
          { email, data: initialData },
        );
        if (error) {
          handleAction({
            action: 'SHOW_MESSAGE',
            params: { title: makeErrorToString(error) || 'Unable to send otp' },
          });
          return dispatch({ type: 'SEND_CODE_ERROR', error });
        } else {
          if (data?.multipassToken) {
            const multipassToken = data?.multipassToken;
            await handleAction({
              action: 'LOGIN_USER_MULTIPASS',
              params: {
                multipassToken: multipassToken,
                method: 'otp',
                redirectAction,
                ignoreLoginRedirect,
              },
            });
          } else if (data?.nextStep === 'custom_action') {
            data?.customAction && handleAction(data.customAction);
          } else {
            dispatch({ type: 'SEND_CODE_SUCCESS', payload: data });
          }
        }
      } catch (error) {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: makeErrorToString(error) || 'Unable to send otp' },
        });
        dispatch({ type: 'SEND_CODE_ERROR', error });
      }
    } else {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: { title: 'Please enter a valid email address' },
      });
    }
  }

  async function verifyCode(otpCode) {
    dispatch({ type: 'VERIFY_CODE', otpCode });
    try {
      const response = await appmaker.applyFilters(
        'appmaker-verify-email-otp',
        {
          email: state?.email,
          otpResponse: state?.otpResponse,
          otpCode,
          redirectAction,
        },
      );
      if (response?.error) {
        dispatch({ type: 'VERIFY_CODE_ERROR', error: response.error });
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: response?.error || 'Unable to verify otp' },
        });
      } else {
        if (response?.multipassToken) {
          const multipassToken = response?.multipassToken;
          await handleAction({
            action: 'LOGIN_USER_MULTIPASS',
            params: {
              multipassToken: multipassToken,
              method: 'otp',
              redirectAction,
              ignoreLoginRedirect,
            },
          });
        } else if (response?.nextStep === 'custom_action') {
          response?.customAction && handleAction(response.customAction);
        }
        dispatch({ type: 'VERIFY_CODE_SUCCESS', payload: response });
      }
    } catch (error) {
      dispatch({ type: 'VERIFY_CODE_ERROR', error });
    }
  }

  async function resendCode(email) {
    dispatch({ type: 'RESEND_CODE' });
    try {
      const { data, error } = await appmaker.applyFilters(
        'appmaker-re-send-email-otp',
        { email, data: initialData },
      );
      if (error) {
        handleAction({
          action: 'SHOW_MESSAGE',
          params: { title: makeErrorToString(error) || 'Unable to send otp' },
        });
        return dispatch({ type: 'RESEND_CODE_ERROR', error });
      }
      dispatch({ type: 'RESEND_CODE_SUCCESS', payload: data });
    } catch (error) {
      handleAction({
        action: 'SHOW_MESSAGE',
        params: { title: makeErrorToString(error) || 'Unable to send otp' },
      });
      dispatch({ type: 'RESEND_CODE_ERROR', error });
    }
  }

  async function reset() {
    dispatch({ type: 'RESET' });
  }

  return {
    ...state,
    sendCode,
    verifyCode,
    resendCode,
    reset,
    otpDigitCount,
  };
}
