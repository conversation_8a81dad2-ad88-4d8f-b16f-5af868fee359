import React, { useMemo, useState, useEffect, useRef } from 'react';
import { Control, useController, useForm } from 'react-hook-form';
import { z } from 'zod';
import { useAction } from '@appmaker-xyz/react-native';
import {
  appmaker,
  runDataSource,
  useUser,
} from '@appmaker-xyz/core';
import { Countries, CountriesDefaultInfo } from '../../data/countriesStateData';
import { getShipsToCountries } from '../../data/countries';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  DropDownItem,
  allCountriesList,
  getShipsToCountriesV2,
} from '../../data/allCountriesList';

// FIXME: @shopify/address can be used to further improve the address form, https://www.npmjs.com/package/@shopify/address

function getProvinces(provincesObject: { [key: string]: string }) {
  const provinceLabels = Object.keys(provincesObject);
  const provinceList = provinceLabels.map((item) => ({
    label: item,
    value: item,
  }));
  return provinceList;
}

const requiredString = (message: string) =>
  z.string({ required_error: message }).min(1, { message });

interface ValidationRules {
  phone?: {
    length?: {
      min?: number;
      max?: number;
      message?: string;
    };
    format?: {
      pattern: RegExp;
      message: string;
    };
  };
  zip?: {
    length?: {
      min?: number;
      max?: number;
      message?: string;
    };
    format?: {
      pattern: RegExp;
      message: string;
    };
  };
}

const getDefaultSchema = (
  rules?: ValidationRules,
  hasProvinces: boolean = true,
) => {
  let phoneSchema = requiredString('Please enter your phone number');
  let zipSchema = requiredString('Please enter your zip/postal code');

  // Apply additional phone validations if provided
  if (rules?.phone?.length) {
    if (rules.phone.length.min) {
      phoneSchema = phoneSchema.min(
        rules.phone.length.min,
        rules.phone.length.message || 'Invalid phone length',
      );
    }
    if (rules.phone.length.max) {
      phoneSchema = phoneSchema.max(
        rules.phone.length.max,
        rules.phone.length.message || 'Invalid phone length',
      );
    }
  }
  if (rules?.phone?.format) {
    phoneSchema = phoneSchema.regex(
      rules.phone.format.pattern,
      rules.phone.format.message,
    );
  }

  // Apply additional zip validations if provided
  if (rules?.zip?.length) {
    if (rules.zip.length.min) {
      zipSchema = zipSchema.min(
        rules.zip.length.min,
        rules.zip.length.message || 'Invalid zip length',
      );
    }
    if (rules.zip.length.max) {
      zipSchema = zipSchema.max(
        rules.zip.length.max,
        rules.zip.length.message || 'Invalid zip length',
      );
    }
  }
  if (rules?.zip?.format) {
    zipSchema = zipSchema.regex(
      rules.zip.format.pattern,
      rules.zip.format.message,
    );
  }

  const baseSchema = {
    country: requiredString('Please enter your country'),
    email: z
      .string({
        required_error: 'Please enter your email address',
      })
      .trim()
      .email('Please enter a valid email address')
      .optional(),
    firstName: requiredString('Please enter your first name'),
    lastName: requiredString('Please enter your last name'),
    company: z.union([z.string(), z.null()]).optional(),
    address1: requiredString('Please enter your address'),
    address2: z.union([z.string(), z.null()]).optional(),
    city: requiredString('Please enter your city'),
    zip: zipSchema,
    phone: phoneSchema,
  };

  // Add province validation only if provinces are available
  if (hasProvinces) {
    return z.object({
      ...baseSchema,
      province: requiredString('Please enter your province'),
    });
  } else {
    return z.object({
      ...baseSchema,
      province: z.string().optional(),
    });
  }
};

export function useAddressForm({
  defaultValues,
  id,
  validationRules,
}: {
  defaultValues?: Partial<FormType>;
  id?: string;
  validationRules?: ValidationRules;
} = {}) {
  // const [updatedSchema, setUpdatedSchema] = useState(schema);
  const [provinceList, setProvinceList] = useState([]);
  const isInitialLoad = useRef(true);
  const previousCountry = useRef<string | null>(null);
  const socialButtons: SocialButton[] = useMemo(
    () => appmaker.applyFilters('social-login-buttons', []) as any[],
    [],
  );
  const onAction = useAction();

  // Create dynamic schema based on province availability
  const hasProvinces = provinceList.length > 0;
  const schema = useMemo(() => {
    const baseSchema = getDefaultSchema(validationRules, hasProvinces);
    return appmaker.applyFilters(
      'appmaker-address-custom-schema',
      baseSchema,
    ) as any;
  }, [validationRules, hasProvinces]);

  const {
    control,
    handleSubmit,
    formState,
    setError,
    setFocus,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const selectedCountry = watch('country');

  // Reset form validation when schema changes (due to province availability)
  useEffect(() => {
    if (!isInitialLoad.current) {
      // Clear any existing validation errors when schema changes
      reset(getValues());
    }
  }, [schema]);

  useEffect(() => {
    if (selectedCountry) {
      const currentValues = getValues();

      const countryArray = Object.values(Countries);
      const countryInfoFromArray = countryArray.find(
        (country) => country.code === selectedCountry,
      );

      if (countryInfoFromArray) {
        const list = getProvinces(countryInfoFromArray.provinces);
        setProvinceList(list);

        // Only clear province if this is not the initial load and country actually changed
        if (
          !isInitialLoad.current &&
          previousCountry.current !== selectedCountry
        ) {
          const currentProvinceExists = list.some(
            (province) => province.value === currentValues.province,
          );
          if (!currentProvinceExists && currentValues.province) {
            currentValues.province = '';
          }
        }
      } else {
        setProvinceList([]);
        // Only clear province if this is not the initial load and country actually changed
        if (
          !isInitialLoad.current &&
          previousCountry.current !== selectedCountry
        ) {
          if (currentValues.province) {
            currentValues.province = '';
          }
        }
      }

      reset(currentValues);

      // Update refs after processing
      if (isInitialLoad.current) {
        isInitialLoad.current = false;
      }
      previousCountry.current = selectedCountry;
    }
  }, [selectedCountry]);
  const [loading, setLoading] = useState(false);
  const { user } = useUser();

  const submitAddress = handleSubmit(async (data) => {
    setLoading(true);
    const dataSource = {
      attributes: {},
      source: 'shopify',
    };

    await new Promise((resolve) => setTimeout(resolve, 2000));
    const [response] = await runDataSource(
      {
        dataSource,
      },
      {
        methodName: id ? 'customerAddressUpdate' : 'customerAddressCreate',
        params: {
          accessToken: user?.accessToken,
          address: {
            ...data,
            id,
          },
        },
      },
    );
    const userErrors =
      response.data?.data?.customerAddressUpdate?.customerUserErrors;

    if (userErrors?.length) {
      userErrors.forEach((error) => {
        setError(error?.field[1], {
          type: 'server',
          message: error.message,
        });
      });
    }

    // need to do api calls here
    setLoading(false);
    onAction({
      action: 'SHOW_MESSAGE',
      params: { title: 'Address saved successfully' },
    });
    onAction({ action: 'GO_BACK' });
  });
  return {
    getShipsToCountries,
    provinceList,
    control,
    handleSubmit,
    formState,
    submitAddress,
    isLoading: loading,
    socialButtons,
    setFocus,
  };
}
type AddressZoneInput = {
  control: Control;
  countryName: string;
  zoneName: string;
  countryValueField?: 'code' | 'name';
  zoneValueField?: 'code' | 'name';
};

type ZoneInputType = 'dropdown' | 'input';
type AddressZoneOutput = {
  zoneList: DropDownItem[];
  countryList: DropDownItem[];
  selectedCountry: string;
  selectedZone: string;
  zoneLabel: string;
  countryLabel: string;
  setCountry: (country: string) => void;
  setZone: (zone: string) => void;
  zoneInputType: ZoneInputType;
};
export function useAddressZone({
  control,
  countryName,
  zoneName,
  countryValueField = 'name',
  zoneValueField = 'name',
}: AddressZoneInput): AddressZoneOutput {
  const { field: countryField } = useController({
    name: countryName,
    control,
  });
  const { field: zoneField } = useController({
    name: zoneName,
    control,
  });
  const selectedCountry = countryField.value;
  const selectedZone = zoneField.value;
  const [zoneList, setZoneList] = useState<DropDownItem[]>([]);
  const isInitialLoad = useRef(true);
  const previousCountry = useRef<string | null>(null);
  const countryList = useMemo<Array<DropDownItem>>(
    () =>
      getShipsToCountriesV2({
        valueField: countryValueField,
      }),
    [],
  );
  const setCountry = (country: string) => {
    countryField.onChange(country);
  };
  const setZone = (zone: string) => {
    zoneField.onChange(zone);
  };
  const zoneInputType = 'dropdown';
  const zoneLabel = 'State';
  const countryLabel = 'Country';
  useEffect(() => {
    if (selectedCountry) {
      const countryInfo = allCountriesList.find(
        (country) => country[countryValueField] === selectedCountry,
      );
      if (countryInfo) {
        const list = countryInfo.zones.map((item) => ({
          label: item.name,
          value: item[zoneValueField],
        }));
        setZoneList(list);

        // Only clear zone if this is not the initial load and country actually changed
        if (
          !isInitialLoad.current &&
          previousCountry.current !== selectedCountry
        ) {
          const currentZoneExists = list.some(
            (zone) => zone.value === selectedZone,
          );
          if (!currentZoneExists && selectedZone) {
            zoneField.onChange('');
          }
        }
      } else {
        // Handle the case where there is no match for the selectedCountry
        setZoneList([]);
        // Only clear zone if this is not the initial load and country actually changed
        if (
          !isInitialLoad.current &&
          previousCountry.current !== selectedCountry
        ) {
          if (selectedZone) {
            zoneField.onChange('');
          }
        }
      }

      // Update refs after processing
      if (isInitialLoad.current) {
        isInitialLoad.current = false;
      }
      previousCountry.current = selectedCountry;
    }
  }, [selectedCountry]);
  return {
    zoneList,
    countryList,
    selectedCountry,
    selectedZone,
    zoneLabel,
    countryLabel,
    setCountry,
    setZone,
    zoneInputType,
  };
}
