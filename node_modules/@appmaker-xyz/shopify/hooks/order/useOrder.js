import { useImmer } from 'use-immer';
import { appmakerFunctions, appPluginStoreApi } from '@appmaker-xyz/core';
import { useEffect } from 'react';
import { NavigationAction } from '../../navigation';
import { currencyHelper } from '../../helper';

var moment = require('moment');

export function useFullfillmentStatus(status, { order }) {
  const [fullfillmentStatus, updateFullfillmentStatus] = useImmer({
    status: '',
    label: '',
    loading: true,
  });
  async function updateStatus() {
    updateFullfillmentStatus((draft) => {
      draft.loading = true;
    });
    const input = {
      status,
      label: status,
    };
    const newStatus = await appmakerFunctions.runAppmakerFunction(
      'custom-shopify-order-fulfillment-status',
      input,
      { order },
    );
    updateFullfillmentStatus((draft) => {
      draft.loading = false;
      draft.status = newStatus?.status;
      draft.label = newStatus?.label;
    });
  }
  useEffect(() => {
    updateStatus(status);
  }, []);
  return fullfillmentStatus;
}

export function useOrderItem(props) {
  const { attributes, onAction, pageDispatch } = props;

  const {
    totalShippingPrice,
    email,
    billingAddress,
    subtotalPrice,
    totalTax,
    shippingAddress,
    totalPrice,
  } = attributes?.blockItem?.node;
  const order = attributes?.blockItem?.node;
  const orderItems = order?.lineItems?.edges.map((item) => item.node);
  const orderDate = moment(order?.processedAt).format('MMM Do YY');
  const addressName =
    order?.shippingAddress?.firstName + ' ' + order?.shippingAddress?.lastName;
  const address1 = order?.shippingAddress?.address1;
  const address2 = order?.shippingAddress?.address2;
  const city = order?.shippingAddress?.city;
  const country = order?.shippingAddress?.country;
  const zip = order?.shippingAddress?.zip;
  const phone = order?.shippingAddress?.phone;
  const orderTotalAmountWithCurrency =
    order?.totalPrice?.amount &&
    currencyHelper(order.totalPrice.amount, order.totalPrice.currencyCode);
  const hideOrderFulfillment =
    appPluginStoreApi().getState().plugins?.shopify?.settings
      ?.hideOrderFulfillment;
  const openOrderDetail = async ({ openById = false } = {}) => {
    let orderOpenAction = NavigationAction.openOrderDetail({
      orderItem: attributes?.blockItem,
    });
    if (openById) {
      orderOpenAction = NavigationAction.openOrderDetailById({
        orderId: attributes?.blockItem?.node?.id,
      });
    }
    if (orderOpenAction && onAction) {
      onAction(orderOpenAction);
    }
  };
  return {
    ...attributes,
    openOrderDetail,
    orderId: order?.name?.replace('#', ''),
    order,
    totalShippingPrice,
    email,
    billingAddress,
    subtotalPrice,
    totalTax,
    shippingAddress,
    totalPrice,
    orderDate,
    lineItems: orderItems,
    addressName,
    address1,
    address2,
    city,
    country,
    zip,
    phone,
    status: order?.fulfillmentStatus,
    paymentMethod: order?.financialStatus,
    orderFulfillment: order?.successfulFulfillments,
    numberOfItems: order?.lineItems?.edges?.length,
    withCurrency: currencyHelper,
    totalPrice: order?.totalPrice?.amount,
    orderTotalAmountWithCurrency,
    hideOrderFulfillment,
  };
}
