
//  order line item hook
export default function useFulfillments(props) {
  const lineItem = props?.attributes?.blockItem;
  const trackingCompany = lineItem?.trackingCompany;
  const images = lineItem?.fulfillmentLineItems?.nodes?.map(
    (item) => item?.lineItem?.variant?.image?.url,
  );
  const fulfillmentLineItemsQuantity =
    lineItem?.fulfillmentLineItems?.nodes?.length;
  const trackingInfo = lineItem?.trackingInfo;

  return {
    trackingCompany,
    images,
    fulfillmentLineItemsQuantity,
    trackingInfo,
  };
}
