import { usePageState } from '@appmaker-xyz/core'
interface PageState {
  _lineItemAttributes?: Record<string, string>;
  setPageState: (newState: Partial<PageState>) => void;
}

type SetValueFunction = (newValue: string) => void;

export function useLineItemAttributes(fieldName: string): [string, SetValueFunction] {
  const lineItemAttributes = usePageState((state: any) => state._lineItemAttributes || {});
  const setPageState = usePageState((state:any) => state.setPageState);

  const value = lineItemAttributes[fieldName] || '';

  const setValue: SetValueFunction = (newValue) => {
    setPageState({
      _lineItemAttributes: {
        ...lineItemAttributes,
        [fieldName]: newValue,
      },
    });
  };

  return [value, setValue];
}
