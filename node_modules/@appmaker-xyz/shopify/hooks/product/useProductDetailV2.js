import { useState, useEffect } from 'react';
import {
  usePageState,
  appSettings,
  getExtensionAsBoolean,
  analytics,
} from '@appmaker-xyz/core';
import { currencyHelper } from '../../helper/index';
import { useProductCartQuantity } from '../useProductCartQuantity';
const formatAttributes = (attr) => {
  if (attr && !Array.isArray(attr)) {
    return Object.keys?.(attr)?.map?.((key) => ({
      key,
      value: attr[key],
    }));
  }
  return attr;
};
function useProductDetailV2(props) {
  const { showAddedToCartMessage = true } = props;
  const onAction = props?.onAction;
  const product = props?.blockData?.node;
  const variants = product?.variants;
  const [addtoCartLoading, setAddtoCartLoading] = useState(false);
  const [buyNowLoading, setBuyNowLoading] = useState(false);
  const selectedVariant = usePageState((state) => state.variant);
  const lineItemAttributes = usePageState((state) => state._lineItemAttributes);
  const addonItems = usePageState((state) => state.lineItemsToAdd);
  const [count, setCount] = useState(1);
  const stateQuantity = usePageState((state) => state.quantity);
  const [qty, cartLineItem] = useProductCartQuantity({
    variantId: selectedVariant?.node?.id,
  });

  const preOrderEnabled = getExtensionAsBoolean?.(
    'shopify',
    'enable_pre_order_on_all_out_of_stock_products',
    false,
  );
  useEffect(() => {
    setCount(qty);
  }, [qty]);
  const referrer = props?.currentAction?.params?.referrer;
  const regularPrice = parseFloat(
    selectedVariant?.node?.compareAtPrice?.amount || 0,
  );
  const shouldConsiderVariantStockCount = getExtensionAsBoolean(
    'shopify',
    'enable_variant_level_quantity_check',
    false,
  );

  const checkStockAvailability = (requestedQuantity) => {
    if (shouldConsiderVariantStockCount) {
      const variantStockQuantity = selectedVariant?.node?.quantityAvailable;
      if (variantStockQuantity - (qty + requestedQuantity) < 0) {
        return {
          success: false,
          availableQuantity: variantStockQuantity
        };
      }
    }
    return {
      success: true
    };
  };

  const addToCart = async ({
    buynow = false,
    appmakerAction = false,
    variant,
  }) => {
    let customAttributes = lineItemAttributes
      ? formatAttributes(lineItemAttributes)
      : [];
    const variantToAdd = variant || selectedVariant;
    if (isDisablePurchase()) {
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'This product is not available for purchase',
        },
      });
      return;
    }
    if (shouldConsiderVariantStockCount) {
      const variantStockQuanity = variantToAdd?.node?.quantityAvailable;
      if (variantStockQuanity - (qty + stateQuantity) < 0) {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: `Not enough items available. Only ${variantStockQuanity} left.`,
            buttonTitle: 'View Cart',
            buttonAction: {
              action: 'OPEN_CART',
            }
          },
        });
        return;
      }
    }
    const setCartLoadingIndicator = buynow
      ? setBuyNowLoading
      : setAddtoCartLoading;

    setCartLoadingIndicator(true);
    try {
      let action = {};
      if (addonItems?.length) {
        const variantToAdd = (variant || selectedVariant)?.node?.id;
        const allItems = addonItems.concat({
          variantId: variantToAdd,
          quantity: stateQuantity || 1,
          ...(!!(customAttributes?.length) && {
            customAttributes,
          }),
        });
        const lineItemsToAdd = allItems.map((item) => ({
          variantId: item.variantId,
          quantity: stateQuantity || 1,
          ...(item?.customAttributes && {
            customAttributes: Array.isArray(item?.customAttributes)
              ? item?.customAttributes
              : formatAttributes(item?.customAttributes),
          }),
        }));
        action = {
          action: 'MANAGE_CART',
          params: {
            lineItemsToAdd: lineItemsToAdd,
          },
        };
      } else {
        action = {
          action: 'ADD_TO_CART_V2',
          params: {
            product: product,
            quantity: stateQuantity || 1,
            variant: variantToAdd,
            customAttributes,
            ...(referrer && { referrer }),
          },
        };
      }
      if (appmakerAction) {
        action = appmakerAction;
      }
      if (buynow) {
        analytics.track(
          'product_buy_now',
          {
            lineItem: {
              variantId: variantToAdd?.node?.id,
              quantity: stateQuantity || 1,
              currency: currency,
              price: salePrice,
              ...(customAttributes && { attributes: customAttributes }),
            },
          },
          { postAction: 'cart', product: product },
        );
      }
      const cartResp = await onAction(action);
      if (!buynow && cartResp?.status === 'success' && showAddedToCartMessage) {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Added to cart',
            buttonTitle: 'View Cart',
            buttonAction: {
              action: 'OPEN_CART',
            },
          },
        });
      } else if (cartResp?.status === 'fail' && cartResp?.message) {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message,
          },
        });
      }
      if (buynow) {
        onAction({ action: 'OPEN_CART' });
      }
      return cartResp;
    } catch (error) {
      console.log(error);
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: error.message || error.data.message,
        },
      });
    } finally {
      setCartLoadingIndicator(false);
    }
  };

  const updateCart = async ({ quantity = 1, appmakerAction = false }) => {
    const setAdding = setAddtoCartLoading;
    try {
      setAdding(true);
      // alert(`reached quantity b ${quantity}`);
      let action = appmakerAction || {
        action: 'UPDATE_CART_V2',
        params: {
          updateCartPageStateRequired: false,
          product,
          variant: selectedVariant,
          quantity,
          lineItemId: cartLineItem.id,
          ...(referrer && { referrer }),
        },
      };
      // TODO message handle on completion
      const cartResp = await onAction(action);
      setAdding(false);
    } catch (error) {
      console.log(error, 'error');
      onAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Something went wrong',
        },
      });
      setAdding(false);
    }
  };
  const salePrice = parseFloat(selectedVariant?.node?.price?.amount || 0);
  const onSale = regularPrice > salePrice;
  const salePercentage = onSale
    ? `${Math.round((100 * (regularPrice - salePrice)) / regularPrice)} %`
    : null;
  const currency =
    selectedVariant?.node?.price?.currencyCode ||
    selectedVariant?.node?.compareAtPrice?.currencyCode;
  const regularPriceWithCurrency = currencyHelper(regularPrice, currency);
  const salePriceWithCurrency = currencyHelper(salePrice, currency);
  const vendorName = product?.vendor;
  const currentVariantInStock = selectedVariant?.node?.availableForSale;
  const hasTag = (tag) => {
    return product?.tags?.includes(tag);
  };
  const buyNow = async (options = {}) => {
    if (onAction) {
      const variantId = selectedVariant?.id || selectedVariant?.node?.id;
      const { walletPreference, ...otherOptions } = options;
      
      analytics.track(
        'product_buy_now',
        {
          lineItem: {
            variantId: variantId,
            quantity: stateQuantity || 1,
            currency: currency,
            price: salePrice,
          },
        },
        { postAction: 'checkout', product: product },
      );
      setBuyNowLoading(true);
      await onAction({
        action: 'BUY_NOW_V2',
        params: {
          variantId: variantId,
          quantity: stateQuantity || 1,
          ...(walletPreference && { walletPreference }),
          ...otherOptions,
        },
      });
      setBuyNowLoading(false);
    }
  };
  const isBuyNowOnly = () => {
    return hasTag?.('_appmaker_buy_now_only');
  };
  const isDisablePurchase = () => {
    return (
      hasTag?.('_appmaker_disable_purchase') ||
      product?.appmaker_hide_product_status?.value === 'true' ||
      product?.appmaker_hide_product_status?.value === true
    );
  };
  const PDPButtonAttributes = {
    addToCartText: 'Add to Cart',
    addToCartButtonColor:
      appSettings.getOption('primary_button_color') || '#000',
    addCartFontColor:
      appSettings.getOption('primary_button_text_color') || '#FFFFFF',
    buyNowText: 'Buy Now',
    buyNowButtonColor:
      appSettings.getOption('secondary_button_color') || '#4F46E5',
    buyNowFontColor:
      appSettings.getOption('secondary_button_text_color') || '#FFFFFF',
    outOfStockText: 'Out of Stock',
    outOfStockButtonColor: '#E5E7EB',
    outOfStockFontColor: '#6B7280',
  };
  return {
    ...PDPButtonAttributes,
    isBuyNowOnly: isBuyNowOnly(),
    isDisablePurchase: isDisablePurchase(),
    buyNowLoading,
    buyNow,
    product,
    tags: product?.tags,
    hasTag,
    vendorName,
    updateCart,
    addtoCartLoading,
    addToCart,
    setCount,
    count,
    salePercentage,
    onSale,
    salePrice,
    regularPrice,
    currentVariantInStock,
    regularPriceWithCurrency:
      regularPrice === salePrice ? '' : regularPriceWithCurrency,
    salePriceWithCurrency,
    selectedVariant,
    preOrderEnabled,
    checkStockAvailability
  };
}

export { useProductDetailV2 };
