import { usePageState } from '@appmaker-xyz/core';
type PageState = {
  productQuantity: number;
};

type SetPageState = (pageState: PageState) => void;

interface UseProductCountReturn {
  setProductQuantity: (quantity: number) => void;
  productQuantity: number;
}

interface UseProductCountProps {
  minQuantity?: number;
  maxQuantity?: number;
}
const useProductQuantity = (
  props?: UseProductCountProps,
): UseProductCountReturn => {
  const { minQuantity = 1, maxQuantity } = props || {};
  const setPageState: SetPageState = usePageState(
    (state) => state.setPageState,
  );
  const productQuantity: number =
    usePageState((state) => state.productQuantity) || 1;
  const setProductQuantity = (quantity: number): void => {
    if (
      typeof quantity === 'number' &&
      quantity >= minQuantity &&
      (maxQuantity === undefined || quantity <= maxQuantity)
    ) {
      setPageState({
        productQuantity: quantity,
      });
    }
  };
  return {
    setProductQuantity,
    productQuantity,
  };
};

export { useProductQuantity };
